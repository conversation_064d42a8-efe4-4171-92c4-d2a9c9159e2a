---
image:
  repository: 664315256653.dkr.ecr.us-east-1.amazonaws.com/mms/api-registry

xgenAppNameOverride: null

replicaCount: 2

env:
  GRPC_LISTEN_ADDRESS: "0.0.0.0:8080"
  HTTP_LISTEN_ADDRESS: "0.0.0.0:8081"
  METRICS_LISTEN_ADDRESS: "0.0.0.0:9090"
  SECRETS_DIR: /run/secrets/10gen.cc/external-secrets/mms
  XGEN_APP_OVERRIDE: api-registry

listeners:
  default:
    appProtocol: grpc
    port:
      containerPort: 8080
      protocol: TCP
  http:
    appProtocol: http
    port:
      name: http
      containerPort: 8081
      protocol: TCP

metricListeners:
  metrics:
    port:
      containerPort: 9090 # Main metrics port for prometheus to scrape. In most cases this should not be changed.
    podMonitor: # PodMonitor defines monitoring for a set of pods.
      podMetricsEndpoints:
        - path: "/metrics"
          scheme: "http"
      podTargetLabels:
        - app
        - environment
        - instance_id

# readiness and startup probes rely on innit to proxy requests to the gRPC app
readinessProbe:
  httpGet:
    port: 8882
    path: /health

livenessProbe:
  httpGet:
    port: 8882
    path: /health

startupProbe:
  httpGet:
    port: 8882
    path: /health
  tcpSocket: null
  initialDelaySeconds: 10
  failureThreshold: 30

authnServiceEnvoyFilter:
  enabled: true
  rules:
    - match:
        safe_regex:
          google_re2: {}
          regex: "^/rest/unauth/version"
    - match:
        safe_regex:
          google_re2: {}
          regex: "^/api/atlas/v2/openapi/info"
    - match:
        safe_regex:
          google_re2: {}
          regex: "^/api/atlas/v2/unauth/.*"
  ports:
    - 8080
    - 8081

headlessService:
  enable: true

gateway:
  api:
    enabled: true
    port: 8081
    paths:
      - name: "info-path"
        path: "/api/atlas/v2/openapi/info"
      - name: "versions-path"
        path: "/api/atlas/v2/unauth/openapi/versions"
