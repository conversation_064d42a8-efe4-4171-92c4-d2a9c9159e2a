# EPA/CFA Migration Strategy

## Overview

This document outlines the strategy for implementing Endpoint Actions (EPA) across ALL MMS endpoints and Customer Facing Actions (CFA) for selected sample endpoints.

## Project Scope

### Universal EPA Implementation
- **Target**: ALL MMS endpoints must have EPA values
- **Timeline**: Current project scope
- **Pattern**: `epa.{resourceType}.{className}.{methodName}.{httpMethod}`
- **Auto-generation**: Build tools will populate EPA values

### Selective CFA Implementation  
- **Target**: SUBSET of endpoints (samples for proof-of-concept)
- **Purpose**: Demonstrate CFA concept and establish patterns
- **Selection Criteria**: High-visibility, customer-facing APIs
- **Future**: Expand based on learnings from samples

## Implementation Phases

### Phase 1: Infrastructure Setup ✅
- [x] Extended Auth annotation with EPA/CFA fields
- [x] Updated authorization processing logic
- [x] Created validation framework
- [x] EPA auto-generation capability

### Phase 2: Universal EPA Rollout
**Goal**: Every MMS endpoint has EPA

#### Step 1: Inventory & Analysis
```bash
# Find all MMS endpoints with @Auth, @UiCall, or @RolesAllowed
bazel run //server/src/main/com/xgen/cloud/authz_tools:GenerateAuthzEndpointActionsTool -- --check
```

#### Step 2: Auto-generate EPA Values
```bash
# Generate EPA for all MMS endpoints
bazel run //server/src/main/com/xgen/cloud/authz_tools:GenerateAuthzEndpointActionsTool -- --populate-epa
```

#### Step 3: Validate EPA Coverage
- Build-time validation ensures no MMS endpoint lacks EPA
- EPA namespace validation (`epa.*` prefix)
- Resource type consistency checks

### Phase 3: Sample CFA Implementation
**Goal**: Demonstrate CFA on selected endpoints

#### CFA Sample Selection Criteria:
1. **High-visibility APIs**: Cluster management, project operations
2. **Customer-facing**: Public API endpoints
3. **Representative coverage**: Different resource types
4. **Team diversity**: Multiple teams for feedback

#### Proposed CFA Samples:
```java
// Project Management
@Auth(
  endpointAction = "epa.project.ProjectResource.createProject.POST",
  customerFacingAction = "projects.create",
  resourceType = "project"
)

// Cluster Operations  
@Auth(
  endpointAction = "epa.cluster.ClusterResource.getClusters.GET", 
  customerFacingAction = "clusters.read",
  resourceType = "cluster"
)

// Organization Management
@Auth(
  endpointAction = "epa.organization.OrgResource.updateSettings.PUT",
  customerFacingAction = "organizations.settings.update", 
  resourceType = "organization"
)
```

## Technical Implementation Details

### EPA Auto-Generation Strategy

**Current Pattern**: 
```
epa.{resourceType}.{className}.{methodName}.{httpMethod}
```

**Examples**:
- `epa.project.ClusterResource.createCluster.POST`
- `epa.organization.UserResource.getUsers.GET`
- `epa.cluster.BackupResource.restoreBackup.PUT`

**Resource Type Mapping**:
- Extract from `@UiCall(roles = RoleSet.GROUP_*)` → `project`
- Extract from `@UiCall(roles = RoleSet.ORG_*)` → `organization`  
- Extract from `@UiCall(roles = RoleSet.GLOBAL_*)` → `global`
- Cluster endpoints → `cluster`

### CFA Naming Conventions

**Pattern**: `{resource}.{subresource}.{operation}`

**Examples**:
- `projects.create` (simple operation)
- `projects.clusters.read` (nested resource)
- `organizations.users.invite` (specific action)
- `clusters.backups.restore` (complex operation)

**Guidelines**:
- Use lowercase, dot-separated format
- Start with primary resource type
- End with operation verb
- Keep hierarchical and intuitive

## Migration Execution Plan

### Week 1-2: EPA Universal Rollout
1. **Day 1-3**: Run EPA auto-generation tool across codebase
2. **Day 4-5**: Fix any validation errors or edge cases
3. **Day 6-7**: Update build validation to enforce EPA requirement
4. **Week 2**: Team review and refinement

### Week 3: CFA Sample Implementation  
1. **Day 1-2**: Select 10-15 sample endpoints across teams
2. **Day 3-4**: Define CFA values for samples
3. **Day 5**: Implement CFA values in annotations
4. **Weekend**: Validation and testing

### Week 4: Validation & Documentation
1. **Day 1-2**: Comprehensive testing of EPA/CFA system
2. **Day 3-4**: Update documentation and guidelines
3. **Day 5**: Team training and knowledge sharing

## Validation & Quality Assurance

### Build-Time Validation
```java
// Enforce EPA for all MMS endpoints
if (isMmsEndpoint && auth.endpointAction().isEmpty()) {
  throw new ValidationException("MMS endpoint must have EPA");
}

// Validate EPA format
if (!epa.matches("^epa\\.(organization|project|cluster|global)\\..*")) {
  throw new ValidationException("Invalid EPA format");
}

// Ensure CFA uniqueness (for samples)
if (cfaAlreadyUsed(auth.customerFacingAction())) {
  throw new ValidationException("CFA must be unique");
}
```

### Testing Strategy
1. **Unit Tests**: Validation logic, authorization processing
2. **Integration Tests**: End-to-end authorization flows
3. **Regression Tests**: Ensure existing S2S auth still works
4. **Performance Tests**: No degradation in auth performance

## Risk Mitigation

### Risk 1: EPA Generation Conflicts
- **Mitigation**: Comprehensive validation before rollout
- **Fallback**: Manual EPA assignment for edge cases

### Risk 2: Authorization Failures
- **Mitigation**: Gradual rollout with feature flags
- **Fallback**: Temporary bypass for critical endpoints

### Risk 3: CFA Naming Conflicts
- **Mitigation**: Central registry and validation
- **Fallback**: Namespace CFA values by team if needed

## Success Metrics

### EPA Rollout Success
- [ ] 100% of MMS endpoints have EPA values
- [ ] 0 build failures due to missing EPA
- [ ] EPA format compliance: 100%
- [ ] No authorization regressions

### CFA Sample Success  
- [ ] 10-15 sample endpoints with CFA
- [ ] CFA uniqueness: 100%
- [ ] Team feedback collected
- [ ] Documentation complete

## Future Considerations

### Post-Project Expansion
1. **CFA Expansion**: Based on sample learnings
2. **Cedar Schema**: Update to include EPA/CFA relationships
3. **Customer APIs**: Expose CFA values in customer-facing APIs
4. **Governance**: Establish CFA approval process

### Long-term Architecture
1. **Constants Files**: Move from direct strings to team constants
2. **IDE Integration**: Auto-completion and validation
3. **Analytics**: Track EPA/CFA usage patterns
4. **Deprecation**: Phase out legacy ActionConstants for MMS

## Conclusion

This migration strategy ensures:
- ✅ Universal EPA coverage for all MMS endpoints
- ✅ Proof-of-concept CFA implementation
- ✅ Backward compatibility with existing authorization
- ✅ Foundation for future EPA/CFA expansion

The approach minimizes risk while delivering the core requirements of the EPA/CFA authorization enhancement project.
