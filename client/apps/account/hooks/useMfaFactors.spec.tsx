import { expect } from 'chai';
import sinon, { SinonSandbox, SinonStub } from 'sinon';

import * as api from '@packages/common/services/api';
import useMfaFactors from '@packages/hooks/useMfaFactors';
import { act, cleanup, render, screen } from '@packages/react-testing-library';

// Dummy component to be used for testing the hook
function Component() {
  const { factors, isLoading, userFactorsLoadError } = useMfaFactors();

  const body = userFactorsLoadError ? (
    <div>Error Message</div>
  ) : (
    <ul>
      {factors.map((factor) => (
        <li key={factor.id}>{factor.id}</li>
      ))}
    </ul>
  );

  return <>{isLoading ? <div>Loading</div> : body} </>;
}

describe('@apps/account/hooks/useGetUserFactors', () => {
  let sandbox: SinonSandbox;
  let getUserFactorsStub: SinonStub;

  beforeEach(async () => {
    sandbox = sinon.createSandbox();
    getUserFactorsStub = sandbox.stub(api.accountMultiFactorAuth, 'getUserFactors');
  });

  afterEach(() => {
    cleanup();
    sandbox.restore();
  });

  const doesNotRenderFactorsList = () => {
    it('does not render the factors list', () => {
      expect(screen.queryByRole('list')).to.not.exist;
    });
  };

  const doesNotRenderErrorMessage = () => {
    it('does not render the error message', () => {
      expect(screen.queryByText('Error Message')).to.not.exist;
    });
  };

  const doesNotRenderLoader = () => {
    it('does not render the loader', async () => {
      expect(screen.queryByText('Loading')).to.not.exist;
    });
  };

  describe('when used and is loading', () => {
    beforeEach(() => {
      // eslint-disable-next-line testing-library/no-render-in-lifecycle
      render(<Component />);
    });

    it('renders the loading state', () => {
      expect(screen.getByText('Loading')).to.exist;
    });

    it('calls the getUserFactors', () => {
      expect(getUserFactorsStub).to.have.been.calledOnce;
    });

    doesNotRenderErrorMessage();

    doesNotRenderFactorsList();
  });

  describe('when the api call returns successfully', () => {
    beforeEach(async () => {
      getUserFactorsStub.resolves([
        {
          id: 'lkn1o0sa9dQCWkxlk986',
          factorType: 'token:software:totp',
          provider: 'OKTA',
        },
        {
          id: 'msp3rep1dzyyPzZw1812',
          factorType: 'push',
          provider: 'OKTA',
        },
        {
          id: 'zsw1o0sa9dXUKkxlk357',
          factorType: 'token:software:totp',
          provider: 'GOOGLE',
        },
      ]);
      // eslint-disable-next-line testing-library/no-unnecessary-act
      act(() => {
        // eslint-disable-next-line testing-library/no-render-in-lifecycle
        render(<Component />);
      });
    });

    it('renders all the factors', () => {
      const listItems = screen.getAllByRole('listitem');
      expect(listItems).to.have.length(3);
    });

    doesNotRenderErrorMessage();

    doesNotRenderLoader();
  });

  describe('when the api call returns with an error', () => {
    beforeEach(async () => {
      getUserFactorsStub.rejects({ errorCode: 'BAD_REQUEST' });
      // eslint-disable-next-line testing-library/no-unnecessary-act
      act(() => {
        // eslint-disable-next-line testing-library/no-render-in-lifecycle
        render(<Component />);
      });
    });

    it('renders the error message', () => {
      expect(screen.getByText('Error Message')).to.exist;
    });

    doesNotRenderFactorsList();

    doesNotRenderLoader();
  });
});
