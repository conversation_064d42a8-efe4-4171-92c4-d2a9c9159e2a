import { SocialAuthMethods } from '@packages/types/authMethod';

import analytics, { SEGMENT_EVENTS } from '@packages/common/utils/segmentAnalytics';
import { cleanup, fireEvent, render, RenderResult, waitFor } from '@packages/react-testing-library';

import SocialUnlinkButton from '@apps/account/components/SocialUnlinkButton';

describe('@apps/account/components/SocialUnlinkButton', function () {
  let renderResult: RenderResult;
  let resetPasswordStub;
  let unlinkingUnitiatedStub;
  let analyticsTrackStub;

  function renderWith(provider: SocialAuthMethods) {
    beforeEach(function () {
      resetPasswordStub = jest.fn();
      unlinkingUnitiatedStub = jest.fn();
      analyticsTrackStub = jest.spyOn(analytics, 'track');

      renderResult = render(
        <SocialUnlinkButton
          provider={provider}
          resetPassword={resetPasswordStub}
          onUnlinkInitiated={unlinkingUnitiatedStub}
        />
      );
    });

    afterEach(function () {
      cleanup();
    });
  }

  [
    { provider: SocialAuthMethods.GOOGLE, label: 'Google', logoTitle: "Google's Logo" },
    { provider: SocialAuthMethods.GITHUB, label: 'GitHub', logoTitle: "GitHub's Logo" },
  ].forEach(({ provider, label, logoTitle }) => {
    describe(`when rendered for ${provider}`, function () {
      renderWith(provider);

      it(`renders the button label as ${label}`, async function () {
        expect(await renderResult.findByText(`Unlink from ${label}`)).toBeInTheDocument();
      });

      it(`renders the button logo as ${logoTitle}`, async function () {
        expect(await renderResult.findByTitle(logoTitle)).toBeInTheDocument();
      });

      describe('and confirmation modal is opened', function () {
        beforeEach(async function () {
          analyticsTrackStub.mockReset();
          unlinkingUnitiatedStub.mockReset();
          fireEvent.click(await renderResult.findByText(`Unlink from ${label}`));
          expect(await renderResult.findByText(`Unlink Your ${label} Account`)).toBeInTheDocument();
        });

        describe('and user confirms unlinking', function () {
          const username = '<EMAIL>';
          describe('and reset password request succeeds', function () {
            beforeEach(async function () {
              resetPasswordStub.mockResolvedValue();
              fireEvent.click(await renderResult.findByText('Confirm'));
              await waitFor(() => expect(resetPasswordStub).toHaveBeenCalledWith({ username }));
            });

            it('should send a ux action track event to Segment', function () {
              expect(analyticsTrackStub).toHaveBeenCalledWith(SEGMENT_EVENTS.UX_ACTION_PERFORMED, {
                context: 'Profile Info Page',
                action: `${label} Unlink button clicked`,
              });
            });

            it('resetPassword api is called', function () {
              expect(resetPasswordStub).toHaveBeenCalledTimes(1);
            });

            it('onUnlinkInitiated prop is called', function () {
              expect(unlinkingUnitiatedStub).toHaveBeenCalledTimes(1);
            });
          });

          describe('and reset password request fails', function () {
            beforeEach(async function () {
              resetPasswordStub.mockRejectedValue({ errorCode: 'RATE_LIMITED' });
              fireEvent.click(await renderResult.findByText('Confirm'));
              await waitFor(() => expect(resetPasswordStub).toHaveBeenCalledWith({ username }));
            });

            it('onUnlinkInitiated prop is not called', function () {
              expect(unlinkingUnitiatedStub).not.toHaveBeenCalled();
            });

            it('should not send a ux action track event to Segment', function () {
              expect(analyticsTrackStub).not.toHaveBeenCalled();
            });

            it('displays error message', async function () {
              expect(await renderResult.findByText('Too many attempts, please try again later.')).toBeInTheDocument();
            });
          });
        });
      });
    });
  });
});
