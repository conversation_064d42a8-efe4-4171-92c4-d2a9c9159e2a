{"extends": "../../../../../package.tsconfig.json", "compilerOptions": {"emitDeclarationOnly": false, "jsxImportSource": "@emotion/react"}, "include": ["**/*"], "references": [{"path": "../components"}, {"path": "../packages/api"}, {"path": "../packages/demo-builder-config"}, {"path": "../../../packages/hooks"}, {"path": "../../../packages/workload"}, {"path": "../../../packages/utils"}, {"path": "../../../packages/document-schema"}, {"path": "../packages/preview-status"}, {"path": "../packages/search-pipeline"}, {"path": "../../../../../packages/external/hooks"}]}