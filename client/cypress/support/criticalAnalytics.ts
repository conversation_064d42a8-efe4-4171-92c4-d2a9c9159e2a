declare global {
  namespace Cypress {
    interface Chainable {
      waitForSegmentEventsToBeProcessed: (time?: number) => Chainable<void>;
      spyPageviewCall: (pageviewName: string) => Chainable<void>;
      spyCreateClusterCall: () => Chainable<void>;
      spyClusterDescriptionCall: () => Chainable<void>;
      countMockServerPageviews: (pageviewName: string) => Chainable<Object>;
    }
  }
}

export const SEGMENT_EVENTS = {
  REGISTRATION_SUCCEEDED: 'Registration Succeeded',
  SIGNED_IN: 'Signed In',
  FIRST_TIME_CLOUD_LOGIN: 'First Time Cloud Login',
  FORM_SUBMITTED: 'Form Submitted',
  TEST_ASSIGNMENT_ALLOCATED: 'Test Assignment Allocated',
  GHOST_ASSIGNMENT_ALLOCATED: 'Ghost Assignment Allocated',
  CLUSTER_CREATED: 'Cluster Created',
  PERSONALIZATION_WIZARD_SUBMITTED: 'Atlas Personalization Wizard Form',
} as const;

export const SEGMENT_PAGEVIEWS = {
  REGISTRATION: 'Registration',
  REGISTRATION_SUCCESS: 'Registration Success',
  SIGN_IN: 'Sign In',
} as const;

export const USER_TRAITS = {
  USERNAME: 'username',
  FIRST_NAME: 'first_name',
  LAST_NAME: 'last_name',
  ACTIVE_ORG_ID: 'active_org_id',
} as const;

/**
 * Wait for Segment event(s) to be processed for the provided 'time' parameter or a default time
 * of 10 seconds.
 */
Cypress.Commands.add('waitForSegmentEventsToBeProcessed', (time = 10000) => {
  cy.wait(time);
});

/**
 * Spies Segment pageview (/p) requests made to the Mock Server and assigns the 'pageview' alias when
 * the request matches the provided 'pageviewName' parameter.
 */
Cypress.Commands.add('spyPageviewCall', (pageviewName: string) => {
  cy.intercept('POST', '/p', (req) => {
    if (JSON.parse(req.body).name === pageviewName) {
      req.alias = 'pageview';
    }
  });
});

/**
 * Retrieves the count of pageview (/p) requests in the Mock Server that match the provided 'pageviewName'
 * parameter.
 */
Cypress.Commands.add('countMockServerPageviews', (pageviewName: string) => {
  cy.retrieveMockServerRequests({
    filterCriteria: {
      path: '/p',
      body: {
        name: pageviewName,
      },
    },
  }).then((res) => {
    return res.count;
  });
});

/**
 * Spy on the POST request to create a cluster and assign an alias to
 * it so its request/response can be awaited and used for assertion.
 * NOTE that Serverless instances don't generate this call.
 */
Cypress.Commands.add('spyCreateClusterCall', () => {
  cy.intercept({
    method: 'POST',
    path: `/nds/clusters/*`,
  }).as('createClusterCall');
});

/**
 * Spy on the GET request to get a project's clusters' descriptions and assign an alias to
 * it so its request/response can be awaited and used for assertion.
 * Structure of response is array of ClusterDescription objects (1 per cluster in project).
 * Be aware that this is a VERY noisy call (it's constantly polled on the Cluster Overview page)
 * so only spy on it right before you want to intercept it
 */
Cypress.Commands.add('spyClusterDescriptionCall', () => {
  cy.intercept({
    method: 'GET',
    path: `/nds/clusters/*`,
  }).as('clusterDescription');
});

const assertClusterCreatedEventMatchesClusterDescription = (clusterName?: string) => {
  it(`should send a 'Cluster Created' Segment event`, () => {
    let filterCriteria: any = {
      body: {
        event: SEGMENT_EVENTS.CLUSTER_CREATED,
      },
    };

    if (clusterName) {
      filterCriteria.properties = {
        cluster_name: clusterName,
      };
    }

    cy.retrieveMockServerRequests({
      filterCriteria,
    }).then((res) => {
      // Awaits the request to complete so its response body (clusterDescription)
      // can be used to compare against the request from mock server
      cy.wait('@clusterDescription').then((interception) => {
        cy.location().then((location) => {
          const segmentRequests = res.requests;
          const segmentRequestBody = segmentRequests[0].body;

          const clusterDescriptionResponseBody = interception.response?.body;
          let clusterDescription = clusterDescriptionResponseBody[0];
          if (clusterName) {
            clusterDescription = clusterDescriptionResponseBody.find((cluster) => cluster.name === clusterName);
          }

          expect(segmentRequests.length).eq(1);
          expect(segmentRequestBody?.event).equal(SEGMENT_EVENTS.CLUSTER_CREATED);

          // confirm context data
          expect(segmentRequestBody?.context.userAgent).to.equal(window.navigator?.userAgent);

          // confirm org/project metadata
          expect(segmentRequestBody?.properties.org_id).to.equal(global.MMS.lastCreatedOrgId);
          expect(segmentRequestBody?.properties.project_id).equal(location.pathname.split('/').pop());

          // confirm cluster spec data
          // Dedicated doesn't have the cloud provider as the "backingProvider" field, while Shared and Serverless do
          expect(segmentRequestBody?.properties.cloud_provider).equal(
            clusterDescription.replicationSpecList[0].regionConfigs[0].electableSpecs.backingProvider ||
              clusterDescription.replicationSpecList[0].regionConfigs[0].cloudProvider
          );
          expect(segmentRequestBody?.properties.shard_count).equal(clusterDescription.replicationSpecList[0].numShards);
          expect(segmentRequestBody?.properties.disk_size_gb).equal(clusterDescription.diskSizeGB);

          const isServerless = clusterDescription['@provider'] === 'SERVERLESS';
          if (!isServerless) {
            expect(segmentRequestBody?.properties.instance_size).equal(
              clusterDescription.replicationSpecList[0].regionConfigs[0].electableSpecs.instanceSize
            );
          }
        });
      });
    });
  });
};

export { assertClusterCreatedEventMatchesClusterDescription };
