const defaultOpts = { timeout: 10000 };

const PersonalizationWizardPage = {
  select(labelledBy: string) {
    return cy.get(`[aria-labelledBy="${labelledBy}"]`, defaultOpts);
  },
  selectOption(value: string) {
    return cy.get(`li[value="${value}"]`, defaultOpts);
  },
  combobox(label: string) {
    return cy.get(`input[aria-label="${label}"]`, defaultOpts);
  },
  comboboxOption(label: string) {
    return cy.get(`li[aria-label="${label}"]`, defaultOpts);
  },
  blurCombobox() {
    // Click the page title to close selector
    return cy.get('h3', defaultOpts).click();
  },
  inputOther() {
    return cy.get('input[data-testid="dataTypesOther"]', defaultOpts);
  },
  submitButton() {
    return cy.get('[data-testid="personalization-wizard-navigation-finish"]', defaultOpts);
  },
  skipButton() {
    return cy.get('[data-testid="personalization-wizard-navigation-skip"]', defaultOpts);
  },
};

export default PersonalizationWizardPage;
