import { addExampleCodeLine, cleanExampleCode } from './ConnectDriverFullExample.constants';
import { DRIVER_EXAMPLES_X509 } from './ConnectDriverFullExample.examples-x509';
import { DriverExamples, DriverGetterOptions } from './ConnectDriverFullExample.schema';

function example_c_34({ uri }: DriverGetterOptions) {
  const { uriText } = uri;

  const exampleText = `
mongoc_client_t client = mongoc_client_new ("${uriText}");
db = mongoc_client_get_database (client, "test");
`;

  return {
    exampleText,
  };
}

function example_c_36({ uri, apiVersion, supportsApiVersion }: DriverGetterOptions) {
  const { uriText } = uri;
  const exampleText = `
#include <mongoc/mongoc.h>

int main(void) {
    mongoc_client_t *client = NULL;
    bson_error_t error = {0};
${addExampleCodeLine(!!apiVersion && !!supportsApiVersion, '      mongoc_server_api_t *api = NULL;')}
    mongoc_database_t *database = NULL;
    bson_t *command = NULL;
    bson_t reply = BSON_INITIALIZER;
    int rc = 0;
    bool ok = true;

    // Initialize the MongoDB C Driver.
    mongoc_init();

    client = mongoc_client_new("${uriText}");
    if (!client) {
        fprintf(stderr, "Failed to create a MongoDB client.\\n");
        rc = 1;
        goto cleanup;
    }

${addExampleCodeLine(
  !!apiVersion && !!supportsApiVersion,
  `
    // Set the version of the Stable API on the client
    api = mongoc_server_api_new(MONGOC_SERVER_API_V1);
    if (!api) {
        fprintf(stderr, "Failed to create a MongoDB server API.\\n");
        rc = 1;
        goto cleanup;
    }

    ok = mongoc_client_set_server_api(client, api, &error);
    if (!ok) {
        fprintf(stderr, "error: %s\\n", error.message);
        rc = 1;
        goto cleanup;
    }
    `
)}

    // Get a handle on the "admin" database.
    database = mongoc_client_get_database(client, "admin");
    if (!database) {
        fprintf(stderr, "Failed to get a MongoDB database handle.\\n");
        rc = 1;
        goto cleanup;
    }

    // Ping the database.
    command = BCON_NEW("ping", BCON_INT32(1));
    ok = mongoc_database_command_simple(
        database, command, NULL, &reply, &error
    );
    if (!ok) {
        fprintf(stderr, "error: %s\n", error.message);
        rc = 1;
        goto cleanup;
    }
    bson_destroy(&reply);

    printf("Pinged your deployment. You successfully connected to MongoDB!\\n");

// Perform cleanup.
cleanup:
    bson_destroy(command);
    mongoc_database_destroy(database);
${addExampleCodeLine(!!apiVersion && !!supportsApiVersion, '      mongoc_server_api_destroy(api);')}
    mongoc_client_destroy(client);
    mongoc_cleanup();

    return rc;
}
`;

  return {
    exampleText,
  };
}

function example_c(options: DriverGetterOptions) {
  const { supportsSRV, supportsX509 } = options;
  if (supportsX509) {
    return DRIVER_EXAMPLES_X509.C(options);
  }

  return supportsSRV ? example_c_36(options) : example_c_34(options);
}

function example_cpp(options: DriverGetterOptions) {
  const { uri, supportsX509, apiVersion, supportsApiVersion } = options;
  if (supportsX509) return DRIVER_EXAMPLES_X509['C++'](options);

  const { uriText } = uri;

  const exampleText = `
#include <bsoncxx/json.hpp>
#include <mongocxx/client.hpp>
#include <mongocxx/instance.hpp>

int main()
{
  try
  {
    // Create an instance.
    mongocxx::instance inst{};

    const auto uri = mongocxx::uri{"${uriText}"};
${addExampleCodeLine(
  !!apiVersion && !!supportsApiVersion,
  `
    // Set the version of the Stable API on the client
    mongocxx::options::client client_options;
    const auto api = mongocxx::options::server_api{mongocxx::options::server_api::version::k_version_${apiVersion}};
    client_options.server_api_opts(api);

    // Setup the connection and get a handle on the "admin" database.
    mongocxx::client conn{ uri, client_options };`
)}
${addExampleCodeLine(
  !supportsApiVersion || !apiVersion,
  `
    // Setup the connection and get a handle on the "admin" database.
    mongocxx::client conn{uri};`
)}
    mongocxx::database db = conn["admin"];

    // Ping the database.
    const auto ping_cmd = bsoncxx::builder::basic::make_document(bsoncxx::builder::basic::kvp("ping", 1));
    db.run_command(ping_cmd.view());
    std::cout << "Pinged your deployment. You successfully connected to MongoDB!" << std::endl;
  }
  catch (const std::exception& e)
  {
    // Handle errors
    std::cout<< "Exception: " << e.what() << std::endl;
  }

  return 0;
}`;

  return {
    exampleText,
  };
}

function example_csharp(options: DriverGetterOptions) {
  const { uri, supportsX509, apiVersion, supportsApiVersion } = options;
  if (supportsX509) return DRIVER_EXAMPLES_X509['C# / .NET'](options);

  const { uriText } = uri;

  const exampleText = `
using MongoDB.Driver;
using MongoDB.Bson;

const string connectionUri = "${uriText}";

var settings = MongoClientSettings.FromConnectionString(connectionUri);
${addExampleCodeLine(
  !!apiVersion && !!supportsApiVersion,
  `
// Set the ServerApi field of the settings object to set the version of the Stable API on the client
settings.ServerApi = new ServerApi(ServerApiVersion.V${apiVersion});`
)}

// Create a new client and connect to the server
var client = new MongoClient(settings);

// Send a ping to confirm a successful connection
try {
  var result = client.GetDatabase("admin").RunCommand<BsonDocument>(new BsonDocument("ping", 1));
  Console.WriteLine("Pinged your deployment. You successfully connected to MongoDB!");
} catch (Exception ex) {
  Console.WriteLine(ex);
}
`;

  return {
    exampleText,
  };
}

function example_java_37({ uri, apiVersion, supportsApiVersion }: DriverGetterOptions) {
  const { uriText } = uri;

  const exampleText = `
import com.mongodb.ConnectionString;
import com.mongodb.MongoClientSettings;
import com.mongodb.MongoException;
${addExampleCodeLine(
  !!apiVersion && !!supportsApiVersion,
  `import com.mongodb.ServerApi;
import com.mongodb.ServerApiVersion;`
)}
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import com.mongodb.client.MongoDatabase;
import org.bson.Document;

public class MongoClientConnectionExample {
    public static void main(String[] args) {
        String connectionString = "${uriText}";

${addExampleCodeLine(
  !!apiVersion && !!supportsApiVersion,
  `        ServerApi serverApi = ServerApi.builder()
                .version(ServerApiVersion.V${apiVersion})
                .build();`
)}

        MongoClientSettings settings = MongoClientSettings.builder()
                .applyConnectionString(new ConnectionString(connectionString))
${addExampleCodeLine(!!apiVersion && !!supportsApiVersion, `                .serverApi(serverApi)`)}
                .build();

        // Create a new client and connect to the server
        try (MongoClient mongoClient = MongoClients.create(settings)) {
            try {
                // Send a ping to confirm a successful connection
                MongoDatabase database = mongoClient.getDatabase("admin");
                database.runCommand(new Document("ping", 1));
                System.out.println("Pinged your deployment. You successfully connected to MongoDB!");
            } catch (MongoException e) {
                e.printStackTrace();
            }
        }
    }
}
`;

  return {
    exampleText,
  };
}

function example_java(options: DriverGetterOptions) {
  const { supportsX509 } = options;
  if (supportsX509) return DRIVER_EXAMPLES_X509.Java(options);
  return example_java_37(options);
}

function example_kotlin({ apiType, uri, apiVersion, supportsApiVersion }: DriverGetterOptions) {
  const { uriText } = uri;

  const exampleText = `
import org.bson.Document
import com.mongodb.ConnectionString
import com.mongodb.MongoClientSettings
${addExampleCodeLine(
  !!apiVersion && !!supportsApiVersion,
  `import com.mongodb.ServerApi
import com.mongodb.ServerApiVersion`
)}
${
  apiType === 'sync'
    ? `import com.mongodb.kotlin.client.MongoClient`
    : `import com.mongodb.kotlin.client.coroutine.MongoClient
import kotlinx.coroutines.runBlocking`
}


object MongoClientConnectionExample {

    fun main() {
        // Replace the placeholders with your credentials and hostname
        val connectionString = "${uriText}"

${addExampleCodeLine(
  !!apiVersion && !!supportsApiVersion,
  `        val serverApi = ServerApi.builder()
            .version(ServerApiVersion.V${apiVersion})
            .build()`
)}

        val mongoClientSettings = MongoClientSettings.builder()
            .applyConnectionString(ConnectionString(connectionString))
${addExampleCodeLine(!!apiVersion && !!supportsApiVersion, `            .serverApi(serverApi)`)}
            .build()

        // Create a new client and connect to the server
        MongoClient.create(mongoClientSettings).use { mongoClient ->
            val database = mongoClient.getDatabase("admin")
            ${
              apiType === 'sync'
                ? `database.runCommand(Document("ping", 1))`
                : `runBlocking {
                database.runCommand(Document("ping", 1))
            }`
            }
            println("Pinged your deployment. You successfully connected to MongoDB!")
        }
    }

}
`;

  return { exampleText };
}

function example_mongoose(options: DriverGetterOptions) {
  const { uri, supportsX509 } = options;
  const { uriText } = uri;

  if (supportsX509) return DRIVER_EXAMPLES_X509['Mongoose'](options);

  const exampleText = `
const mongoose = require('mongoose');
const uri = "${uriText}";

const clientOptions = { serverApi: { version: '1', strict: true, deprecationErrors: true } };

async function run() {
  try {
    // Create a Mongoose client with a MongoClientOptions object to set the Stable API version
    await mongoose.connect(uri, clientOptions);
    await mongoose.connection.db.admin().command({ ping: 1 });
    console.log("Pinged your deployment. You successfully connected to MongoDB!");
  } finally {
    // Ensures that the client will close when you finish/error
    await mongoose.disconnect();
  }
}
run().catch(console.dir);
`;

  return {
    exampleText,
  };
}

function example_nodejs_34({ uri }: DriverGetterOptions) {
  const { uriText } = uri;

  const exampleText = `
var MongoClient = require('mongodb').MongoClient;

var uri = "${uriText}";
MongoClient.connect(uri, function(err, client) {
  const collection = client.db("test").collection("devices");
  // perform actions on the collection object
  client.close();
});
`;

  return {
    exampleText,
  };
}

function example_nodejs_36({ uri, apiVersion, supportsApiVersion }: DriverGetterOptions) {
  const { uriText } = uri;

  const exampleText = `
${
  !supportsApiVersion || !apiVersion
    ? "const { MongoClient } = require('mongodb');"
    : "const { MongoClient, ServerApiVersion } = require('mongodb');"
}
const uri = "${uriText}";
${
  !supportsApiVersion || !apiVersion
    ? `
// Create a new MongoClient
const client = new MongoClient(uri, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});`
    : `
// Create a MongoClient with a MongoClientOptions object to set the Stable API version
const client = new MongoClient(uri, {
  serverApi: {
    version: ServerApiVersion.v${apiVersion},
    strict: true,
    deprecationErrors: true,
  }
});`
}

async function run() {
  try {
    // Connect the client to the server	(optional starting in v4.7)
    await client.connect();
    // Send a ping to confirm a successful connection
    await client.db("admin").command({ ping: 1 });
    console.log("Pinged your deployment. You successfully connected to MongoDB!");
  } finally {
    // Ensures that the client will close when you finish/error
    await client.close();
  }
}
run().catch(console.dir);
`;

  return {
    exampleText,
  };
}

function example_nodejs(options: DriverGetterOptions) {
  const { supportsSRV, supportsX509 } = options;
  if (supportsX509) return DRIVER_EXAMPLES_X509['Node.js'](options);

  return supportsSRV ? example_nodejs_36(options) : example_nodejs_34(options);
}

function example_php(options: DriverGetterOptions) {
  const { uri, supportsX509, apiVersion, supportsApiVersion } = options;
  if (supportsX509) return DRIVER_EXAMPLES_X509.PHP(options);

  const { uriText } = uri;

  const exampleText = `
<?php
${addExampleCodeLine(!!apiVersion && !!supportsApiVersion, `use MongoDB\\Driver\\ServerApi;`)}

$uri = '${uriText}';

${addExampleCodeLine(
  !!apiVersion && !!supportsApiVersion,
  `// Set the version of the Stable API on the client
$apiVersion = new ServerApi(ServerApi::V${apiVersion});
`
)}
// Create a new client and connect to the server
$client = new MongoDB\\Client($uri${!!apiVersion && !!supportsApiVersion ? `, [], ['serverApi' => $apiVersion]` : ''});

try {
    // Send a ping to confirm a successful connection
    $client->selectDatabase('admin')->command(['ping' => 1]);
    echo "Pinged your deployment. You successfully connected to MongoDB!\\n";
} catch (Exception $e) {
    printf($e->getMessage());
}`;

  return {
    exampleText,
  };
}

function example_python(options: DriverGetterOptions) {
  const { uri, supportsX509, apiVersion, supportsApiVersion } = options;
  if (supportsX509) return DRIVER_EXAMPLES_X509.Python(options);
  const { uriText } = uri;

  const exampleText = `
from pymongo.mongo_client import MongoClient
${addExampleCodeLine(!!supportsApiVersion && !!apiVersion, 'from pymongo.server_api import ServerApi')}

uri = "${uriText}"

# Create a new client and connect to the server
client = MongoClient(uri${!!supportsApiVersion && !!apiVersion ? `, server_api=ServerApi('${apiVersion}')` : ''})

# Send a ping to confirm a successful connection
try:
    client.admin.command('ping')
    print("Pinged your deployment. You successfully connected to MongoDB!")
except Exception as e:
    print(e)`;

  return {
    exampleText,
  };
}

function example_ruby(options: DriverGetterOptions) {
  const { uri, supportsX509, apiVersion, supportsApiVersion } = options;
  if (supportsX509) return DRIVER_EXAMPLES_X509.Ruby(options);

  const { uriText } = uri;

  const exampleText = `
require 'mongo'

uri = "${uriText}"
${addExampleCodeLine(
  !!apiVersion && !!supportsApiVersion,
  `
# Set the server_api field of the options object to set the version of the Stable API on the client
options = { server_api: {version: "1"} }`
)}

# Create a new client and connect to the server
${addExampleCodeLine(!!apiVersion && !!supportsApiVersion, `client = Mongo::Client.new(uri, options)`)}
${addExampleCodeLine(!apiVersion || !supportsApiVersion, `client = Mongo::Client.new(uri)`)}

# Send a ping to confirm a successful connection
begin
  admin_client = client.use('admin')
  result = admin_client.database.command(ping: 1)
  puts "Pinged your deployment. You successfully connected to MongoDB!"
rescue Mongo::Error::OperationFailure => ex
  puts ex
ensure
  client.close
end
`;

  return {
    exampleText,
  };
}

function example_rust(options: DriverGetterOptions) {
  const { apiType, uri, apiVersion, supportsX509, supportsApiVersion } = options;
  const { uriText } = uri;

  if (supportsX509) return DRIVER_EXAMPLES_X509.Rust(options);

  if (apiType === 'sync') {
    const exampleText = `
${addExampleCodeLine(
  !!apiVersion && !!supportsApiVersion,
  `use mongodb::{bson::doc, options::{ClientOptions, ServerApi, ServerApiVersion}, sync::Client};`
)}
${addExampleCodeLine(
  !supportsApiVersion || !apiVersion,
  `use mongodb::{bson::doc, options::ClientOptions, sync::Client};`
)}

fn main() -> mongodb::error::Result<()> {
  let mut client_options =
    ClientOptions::parse("${uriText}")?;
${addExampleCodeLine(
  !!apiVersion && !!supportsApiVersion,
  `
  // Set the server_api field of the client_options object to set the version of the Stable API on the client
  let server_api = ServerApi::builder().version(ServerApiVersion::V1).build();
  client_options.server_api = Some(server_api);`
)}

  // Get a handle to the cluster
  let client = Client::with_options(client_options)?;

  // Ping the server to see if you can connect to the cluster
  client
    .database("admin")
    .run_command(doc! {"ping": 1}, None)?;
  println!("Pinged your deployment. You successfully connected to MongoDB!");

  Ok(())
}`;
    return {
      exampleText,
    };
  }

  const exampleText = `
${addExampleCodeLine(
  !!apiVersion && !!supportsApiVersion,
  `use mongodb::{bson::doc, options::{ClientOptions, ServerApi, ServerApiVersion}, Client};`
)}
${addExampleCodeLine(!supportsApiVersion || !apiVersion, `use mongodb::{bson::doc, options::ClientOptions, Client};`)}

#[tokio::main]
async fn main() -> mongodb::error::Result<()> {
  let mut client_options =
    ClientOptions::parse("${uriText}")
      $.await?;
${addExampleCodeLine(
  !!apiVersion && !!supportsApiVersion,
  `
  // Set the server_api field of the client_options object to set the version of the Stable API on the client
  let server_api = ServerApi::builder().version(ServerApiVersion::V1).build();
  client_options.server_api = Some(server_api);`
)}

  // Get a handle to the cluster
  let client = Client::with_options(client_options)?;

  // Ping the server to see if you can connect to the cluster
  client
    .database("admin")
    .run_command(doc! {"ping": 1}, None)
    .await?;
  println!("Pinged your deployment. You successfully connected to MongoDB!");

  Ok(())
}`;
  return {
    exampleText,
  };
}

// We're not committed to updating scala for .LIVE 2021
function example_scala(options: DriverGetterOptions) {
  const { uri, apiVersion, supportsX509, supportsApiVersion } = options;
  if (supportsX509) return DRIVER_EXAMPLES_X509.Scala(options);

  const { uriText } = uri;

  const exampleText = `
${addExampleCodeLine(
  !!apiVersion && !!supportsApiVersion,
  `import com.mongodb.{ServerApi, ServerApiVersion}
import org.mongodb.scala.{ConnectionString, MongoClient, MongoClientSettings}`
)}
${addExampleCodeLine(!supportsApiVersion || !apiVersion, `import org.mongodb.scala.MongoClient`)}
import org.mongodb.scala.bson.Document

import scala.concurrent.Await
import scala.concurrent.duration.DurationInt
import scala.util.Using

object MongoClientConnectionExample {

  def main(args: Array[String]): Unit = {

    val connectionString = "${uriText}";
${addExampleCodeLine(
  !!apiVersion && !!supportsApiVersion,
  `
    // Construct a ServerApi instance using the ServerApi.builder() method
    val serverApi = ServerApi.builder.version(ServerApiVersion.V1).build()

    val settings = MongoClientSettings
      .builder()
      .applyConnectionString(ConnectionString(connectionString))
      .serverApi(serverApi)
      .build()`
)}

    // Create a new client and connect to the server
${addExampleCodeLine(!!apiVersion && !!supportsApiVersion, `    Using(MongoClient(settings)) { mongoClient =>`)}
${addExampleCodeLine(!apiVersion || !supportsApiVersion, `    Using(MongoClient(connectionString)) { mongoClient =>`)}
      // Send a ping to confirm a successful connection
      val database = mongoClient.getDatabase("admin")
      val ping = database.runCommand(Document("ping" -> 1)).head()

      Await.result(ping, 10.seconds)
      System.out.println("Pinged your deployment. You successfully connected to MongoDB!")
    }

  }
}
`;

  return {
    exampleText,
  };
}

function example_swift(options: DriverGetterOptions) {
  const { apiType, uri, supportsX509 } = options;
  const { uriText } = uri;

  if (supportsX509) return DRIVER_EXAMPLES_X509.Swift(options);

  if (apiType === 'sync') {
    const exampleText = `
import MongoSwiftSync
let client = try MongoClient(
    "${uriText}"
)
defer {
    cleanupMongoSwift()
}
let db = client.db("testDB")
let collection = db.collection("testCol")
`;
    return {
      exampleText,
    };
  }
  const exampleText = `
import MongoSwift
import NIO
let elg = MultiThreadedEventLoopGroup(numberOfThreads: 4)
let client = try MongoClient(
    "${uriText}",
    using: elg
)
defer {
  try? client.syncClose()
  cleanupMongoSwift()
  try? elg.syncShutdownGracefully()
}

print(try client.listDatabaseNames().wait())
`;
  return {
    exampleText,
  };
}

function example_go(options: DriverGetterOptions) {
  const { uri, supportsX509, apiVersion, supportsApiVersion, baseVersion } = options;
  const { uriText } = uri;

  if (supportsX509) return DRIVER_EXAMPLES_X509.Go(options);

  const exampleTextV1 = `
package main

import (
  "context"
  "fmt"

  "go.mongodb.org/mongo-driver/bson"
  "go.mongodb.org/mongo-driver/mongo"
  "go.mongodb.org/mongo-driver/mongo/options"
)

func main() {
${addExampleCodeLine(
  !!apiVersion && !!supportsApiVersion,
  `  // Use the SetServerAPIOptions() method to set the version of the Stable API on the client
  serverAPI := options.ServerAPI(options.ServerAPIVersion1)
  opts := options.Client().ApplyURI("${uriText}").SetServerAPIOptions(serverAPI)`
)}
${addExampleCodeLine(!supportsApiVersion || !apiVersion, `   opts := options.Client().ApplyURI("${uriText}")`)}

  // Create a new client and connect to the server
  client, err := mongo.Connect(context.TODO(), opts)
  if err != nil {
    panic(err)
  }

  defer func() {
    if err = client.Disconnect(context.TODO()); err != nil {
      panic(err)
    }
  }()

  // Send a ping to confirm a successful connection
  if err := client.Database("admin").RunCommand(context.TODO(), bson.D{{"ping", 1}}).Err(); err != nil {
    panic(err)
  }
  fmt.Println("Pinged your deployment. You successfully connected to MongoDB!")
}
`;

  const exampleTextV2 = `
package main

import (
  "context"
  "fmt"

  "go.mongodb.org/mongo-driver/v2/mongo"
  "go.mongodb.org/mongo-driver/v2/mongo/options"
  "go.mongodb.org/mongo-driver/v2/mongo/readpref"
)

func main() {
  // Use the SetServerAPIOptions() method to set the version of the Stable API on the client
  serverAPI := options.ServerAPI(options.ServerAPIVersion1)
  opts := options.Client().ApplyURI("${uriText}").SetServerAPIOptions(serverAPI)

  // Create a new client and connect to the server
  client, err := mongo.Connect(opts)
  if err != nil {
    panic(err)
  }

  defer func() {
    if err = client.Disconnect(context.TODO()); err != nil {
      panic(err)
    }
  }()

  // Send a ping to confirm a successful connection
  if err := client.Ping(context.TODO(), readpref.Primary()); err != nil {
    panic(err)
  }
  fmt.Println("Pinged your deployment. You successfully connected to MongoDB!")
}
`;

  const exampleText = baseVersion !== '2.0' ? exampleTextV1 : exampleTextV2;

  return {
    exampleText,
  };
}

const DRIVER_EXAMPLES: DriverExamples = {
  C: example_c,
  'C++': example_cpp,
  'C# / .NET': example_csharp,
  Java: example_java,
  Kotlin: example_kotlin,
  Mongoose: example_mongoose,
  'Node.js': example_nodejs,
  PHP: example_php,
  Python: example_python,
  Ruby: example_ruby,
  Rust: example_rust,
  Scala: example_scala,
  Swift: example_swift,
  Go: example_go,
};

const CLEAN_DRIVER_EXAMPLES = {};

Object.keys(DRIVER_EXAMPLES).forEach((key) => {
  // @ts-expect-error TS(7053): Element implicitly has an 'any' type because expre... Remove this comment to see the full error message
  CLEAN_DRIVER_EXAMPLES[key] = (options: DriverGetterOptions) => ({
    exampleText: cleanExampleCode(DRIVER_EXAMPLES[key](options).exampleText),
  });
});

export { CLEAN_DRIVER_EXAMPLES as DRIVER_EXAMPLES };
