import { InstanceSizes } from '@packages/types/nds/provider';

import { getRelevantClusterDraft } from '@packages/cluster-drafts/utils';
import { Routes } from '@packages/common/constants/project';
import clusterFixtures from '@packages/common/fixtures/clusterFixtures';
import { navigateTo } from '@packages/common/utils/navigateTo';
import analytics, { SEGMENT_EVENTS } from '@packages/common/utils/segmentAnalytics';
import { onUpgradeButtonClick } from '@packages/project/common/util/featureWallUtils';
import getTypedMockOf from '@packages/test-utils/getTypedMockOf';

jest.mock('@packages/common/utils/navigateTo');
jest.mock('@packages/common/utils/segmentAnalytics');
jest.mock('@packages/cluster-drafts/utils', () => {
  const actual = jest.requireActual('@packages/cluster-drafts/utils');
  return {
    __esModule: true,
    ...actual,
    default: jest.fn(actual.default),
    getRelevantClusterDraft: jest.fn().mockResolvedValue(null),
  };
});
const navigateToMock = getTypedMockOf(navigateTo);
const getRelevantClusterDraftMock = getTypedMockOf(getRelevantClusterDraft);

const clusterName = 'Cluster0';
const featureName = 'feature';
const groupId = '123';

describe('packages/project/common/util/featureWallUtils.ts', () => {
  describe('when onUpgradeButtonClick is called with a cluster draft', () => {
    beforeEach(() => {
      const clusterDescription = clusterFixtures.getAwsDefaultClusterDescription_asJSON('Cluster0', 'M10');
      const clusterDraft = {
        clusterConfiguration: {
          instanceSize: InstanceSizes.M10,
          clusterDescription,
          searchDeploymentSpec: undefined,
          processArgs: clusterFixtures.getDefaultProcessArgs(),
          tags: undefined,
        },
        draftId: '',
        lastUpdatedTime: '',
        isFromCst: false,
      };
      getRelevantClusterDraftMock.mockResolvedValue(clusterDraft);
      onUpgradeButtonClick({ clusterName, groupId, featureName });
    });
    it('navigates to the cluster edit page', async () => {
      expect(navigateToMock).toHaveBeenCalledWith(
        `${Routes.ClusterBuilder({ clusterName: 'Cluster0', query: 'filter=advanced' })}`
      );
    });

    it('sends an analytics event', () => {
      expect(analytics.track).toHaveBeenCalledWith(SEGMENT_EVENTS.PRODUCT_FEATURE_WALL_BUTTON_CLICKED, {
        context: featureName,
        action: '"Upgrade cluster to access" Button Clicked',
      });
    });
  });
  describe('when onUpgradeButtonClick is called without a cluster draft', () => {
    beforeEach(() => {
      getRelevantClusterDraftMock.mockResolvedValue(null);
      onUpgradeButtonClick({ clusterName, groupId, featureName });
    });
    it('navigates to the cluster upgrade templates page', async () => {
      expect(navigateToMock).toHaveBeenCalledWith(Routes.UpgradeTemplates({ clusterName: 'Cluster0' }));
    });

    it('sends an analytics event', () => {
      expect(analytics.track).toHaveBeenCalledWith(SEGMENT_EVENTS.PRODUCT_FEATURE_WALL_BUTTON_CLICKED, {
        context: featureName,
        action: '"Upgrade cluster to access" Button Clicked',
      });
    });
  });
  describe('when onUpgradeButtonClick is called and the cluster draft request fails', () => {
    beforeEach(() => {
      getRelevantClusterDraftMock.mockRejectedValueOnce('error');
      onUpgradeButtonClick({ clusterName, groupId, featureName });
    });
    it('navigates to the cluster upgrade templates page', async () => {
      expect(navigateToMock).toHaveBeenCalledWith(Routes.UpgradeTemplates({ clusterName: 'Cluster0' }));
    });

    it('sends an analytics event', () => {
      expect(analytics.track).toHaveBeenCalledWith(SEGMENT_EVENTS.PRODUCT_FEATURE_WALL_BUTTON_CLICKED, {
        context: featureName,
        action: '"Upgrade cluster to access" Button Clicked',
      });
    });
  });
});
