import BrandIcon from '@packages/brand-icons';

function EmptyPeeringView() {
  return (
    <div className="empty-view empty-view-has-no-border">
      <BrandIcon
        className="empty-view-image"
        iconName="General_CLOUD_Transfer"
        alt="cloud with circle inside with two arrows, one up and one down"
      />
      <div className="empty-view-text empty-view-text-is-heading">Create a peering connection</div>
      <div className="empty-view-text empty-view-text-is-sub-heading">
        Peer your application VPC with your Atlas VPC
      </div>
      <div className="empty-view-text empty-view-text-is-tertiary">
        <a
          href="https://docs.atlas.mongodb.com/security-vpc-peering/"
          target="_blank"
          rel="noopener noreferrer"
          className="link"
        >
          More information
        </a>
      </div>
    </div>
  );
}

export default EmptyPeeringView;
