import { useState } from 'react';

import { css } from '@emotion/react';
import Banner, { Variant } from '@leafygreen-ui/banner';
import ConfirmationModal from '@leafygreen-ui/confirmation-modal';

import { VPCPeeringConnection } from '@packages/types/nds/streams';

import streamsApi from '@packages/common/services/api/nds/streamsApi';
import { exceptionToMessage } from '@packages/common/services/errorHelper';
import { headerZIndex } from '@packages/common/styles/layoutStyles';

const errorBannerStyle = css({
  marginBottom: 24,
  marginTop: 24,
});

const infoTextStyle = css({
  marginBottom: 20,
});

export const TEST_ID = {
  Modal: 'aws-terminate-inbound-peering-connection-modal',
  Body: 'terminate-inbound-peer-connection-body',
  Error: 'error-banner',
  CautionInfo: 'terminate-inbound-peer-connection-caution',
};

export interface AWSTerminateInboundPeeringConnectionModalProps {
  groupId: string;
  vpcConnection: VPCPeeringConnection;
  onClose: () => void;
  reloadInboundConnections: () => void;
}

export default function AWSTerminateInboundPeeringConnectionModal({
  groupId,
  vpcConnection,
  onClose,
  reloadInboundConnections,
}: AWSTerminateInboundPeeringConnectionModalProps) {
  const [errorMessage, setErrorMessage] = useState('');

  const onClickTerminate = () => {
    const peeringId = vpcConnection.name;
    streamsApi
      .terminateVPCPeeringConnection(groupId, peeringId)
      .then(() => {
        reloadInboundConnections();
        onClose();
      })
      .catch((error) => setErrorMessage(exceptionToMessage(error)));
  };

  return (
    <ConfirmationModal
      css={css`
        z-index: ${headerZIndex + 1};
      `}
      data-testid={TEST_ID.Modal}
      variant="danger"
      title="Terminate Peering Connection"
      confirmButtonProps={{
        onClick: onClickTerminate,
        children: 'Yes, Terminate',
      }}
      cancelButtonProps={{
        onClick: onClose,
      }}
      setOpen={onClose}
      open
    >
      {errorMessage && (
        <Banner data-testid={TEST_ID.Error} css={errorBannerStyle} variant={Variant.Danger}>
          {errorMessage}
        </Banner>
      )}
      <Banner css={infoTextStyle} data-testid={TEST_ID.CautionInfo} variant={Variant.Danger}>
        <strong>
          If you terminate this peering connection, all applications connected via VPC peering will lose access to your
          clusters.
        </strong>
      </Banner>

      <p data-testid={TEST_ID.Body}>
        Are you sure you want to terminate the peering connection with <em>{vpcConnection.accepterVpcId}</em>?
      </p>
    </ConfirmationModal>
  );
}
