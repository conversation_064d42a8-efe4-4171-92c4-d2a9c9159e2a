import { css } from '@emotion/react';
import { palette } from '@leafygreen-ui/palette';
import { spacing } from '@leafygreen-ui/tokens';

export const styles = {
  root: css`
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
  `,
  chevronColumn: css`
    padding-right: ${spacing[2]}px;
  `,
  contents: css``,
  headerWrapper: css`
    width: 328px;
    max-width: 328px;
    padding-bottom: ${spacing[1]}px;
    cursor: pointer;
  `,
  sectionSummary: css`
    font-family: Euclid Circular A;
    font-weight: 600;
    font-size: 13px;
    line-height: 20px;
    color: ${palette.gray.dark2};
  `,
  hiddenSection: css`
    display: none;
  `,
  shownSection: css``,
};
