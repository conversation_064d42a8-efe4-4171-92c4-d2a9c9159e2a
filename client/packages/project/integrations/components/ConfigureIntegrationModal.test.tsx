import Banner from '@leafygreen-ui/banner';
import chai from 'chai';
import chaiEnzyme from 'chai-enzyme';
import enzyme from 'enzyme';
import { Provider } from 'react-redux';
import sinon from 'sinon';
import sinonChai from 'sinon-chai';

import { createMainReduxStore } from '@packages/redux/project/test-utils/reduxHelpers';

import Deferred from '@packages/common/utils/Deferred';
// analytics
import analytics, { SEGMENT_EVENTS } from '@packages/common/utils/segmentAnalytics';
import ConfigureIntegrationModal, {
  ConfigureIntegrationProps,
} from '@packages/project/integrations/components/ConfigureIntegrationModal';

chai.use(chaiEnzyme());
const expect = chai.expect;

chai.use(sinonChai);

const MODAL_CONTENT = '.view-modal-content';
const MODAL_TITLE = '.view-modal-header-title';
const DESCRIPTION = 'ConfigurationRow[data-test-id="description"]';
const LTTP_INPUT = 'input[name="aLinkToThePast"]';
const EARTHBOUND_INPUT = 'input[name="earthbound"]';
const JOUST_INPUT = 'input[name="joust"]';
const VICTOROPS_INPUT = 'input[name="victorOpsRoutingKey"]';
const TEST_BUTTON = 'button[name="testIntegration"]';
const SUBMIT_BUTTON = 'button[name="submit"]';
const CLOSE_BUTTON = 'Button[name="close"]';
const EDIT_BUTTON = 'Button[name="edit"]';
const US_ENDPOINT_INPUT = 'input[value="US"]';
const EU_ENDPOINT_INPUT = 'input[value="EU"]';
const THIRD_PARTY_INTEG_DOC_LINK = '[data-test-id="thirdPartyIntegrationDocsLink"]';
const STANDALONE_THIRD_PARTY_INTEG_DOC_LINK = '[data-test-id="standaloneThirdPartyIntegrationDocsLink"]';

const formSelectors = [
  LTTP_INPUT,
  EARTHBOUND_INPUT,
  JOUST_INPUT,
  US_ENDPOINT_INPUT,
  EU_ENDPOINT_INPUT,
  TEST_BUTTON,
  SUBMIT_BUTTON,
];

describe('@packages/project/integrations/components/ConfigureIntegrationModal', function () {
  beforeEach(function (this: $TSFixMe) {
    this.sandbox = sinon.createSandbox();
    this.onCloseStub = this.sandbox.spy();
    this.configurationStub = this.sandbox.stub();
    this.analyticsSpy = this.sandbox.spy(analytics, 'track');

    // @ts-expect-error TS(6198): All destructured elements are unused.
    const mockActionCreator = ({ groupId, body }: $TSFixMe) => this.configurationStub;

    this.testIntegrationDeferred = new Deferred();
    this.testIntegrationStub = this.sandbox.stub().returns(this.testIntegrationDeferred.promise());

    this.commonProps = {
      open: true,
      integrationName: 'superNintendo',
      integrationDisplayName: 'Super Nintendo',
      showEndpoints: true,
      showTestIntegration: true,
      testIntegration: this.testIntegrationStub,
      description: 'The Super Nintendo was arguably the greatest accomplishment of mankind',
    };

    this.testFormEnabled = function (isEnabled: boolean) {
      formSelectors.forEach((selector) => {
        const element = this.wrapper.find(selector);
        const ariaDisabled = element.prop('aria-disabled');
        expect(ariaDisabled !== undefined ? ariaDisabled : element.prop('disabled')).to.equal(!isEnabled);
      });
    };

    this.testFormAfterError = function () {
      [LTTP_INPUT, EARTHBOUND_INPUT, JOUST_INPUT, US_ENDPOINT_INPUT, EU_ENDPOINT_INPUT].forEach((selector) => {
        const isDisabled = this.wrapper.find(selector).prop('disabled');
        expect(isDisabled).to.not.equal(true);
      });

      [TEST_BUTTON, SUBMIT_BUTTON].forEach((selector) => {
        const isDisabled = this.wrapper.find(selector).prop('aria-disabled');
        expect(isDisabled).to.equal(true);
      });
    };

    this.testFormInvisible = function () {
      formSelectors.forEach((selector) => {
        expect(this.wrapper.find(selector).length).to.equal(0);
      });
    };

    this.testAlert = function ({ variant = 'info', header = '', message = '' }) {
      const alert = this.wrapper.find(Banner);
      expect(alert.length).to.equal(1);
      expect(alert.prop('variant')).to.equal(variant);
      expect(alert.text()).to.contain(header);
      expect(alert.text()).to.contain(message);
    };

    this.renderComponent = (props: ConfigureIntegrationProps) => {
      return enzyme.mount(
        <Provider store={createMainReduxStore({})}>
          <ConfigureIntegrationModal
            {...props}
            integrationScope={{ groupId: 'g01' }}
            onClose={this.onCloseStub}
            configurationActionCreator={mockActionCreator}
          />
        </Provider>
      );
    };
  });

  afterEach(function (this: $TSFixMe) {
    this.sandbox.restore();
  });

  describe('when rendered as closed', function () {
    beforeEach(function (this: $TSFixMe) {
      const requiredProps = {
        integrationName: 'superNintendo',
        integrationDisplayName: 'Super Nintendo',
        inputs: [],
      };

      this.wrapper = this.renderComponent(requiredProps);
    });

    it('does not show the modal', function (this: $TSFixMe) {
      expect(this.wrapper.find(MODAL_CONTENT).length).to.equal(0);
    });
  });

  describe('when rendered with all possible options without initial values', function () {
    beforeEach(function (this: $TSFixMe) {
      const allProps = {
        ...this.commonProps,
        inputs: [
          {
            displayName: 'A Link to the Past',
            name: 'aLinkToThePast',
          },
          {
            displayName: 'Earthbound',
            name: 'earthbound',
            optional: true,
          },
          {
            displayName: 'Joust',
            name: 'joust',
            optional: true,
            requiredForTest: true,
          },
        ],
      };

      this.wrapper = this.renderComponent(allProps);
    });

    it('displays the correct modal title', function (this: $TSFixMe) {
      expect(this.wrapper.find(MODAL_TITLE).text()).to.equal('Super Nintendo Configuration');
    });

    it('displays the correct description', function (this: $TSFixMe) {
      expect(this.wrapper.find(DESCRIPTION).text()).to.contain('greatest accomplishment');
    });

    it('displays three labeled inputs', function (this: $TSFixMe) {
      const lttpInput = this.wrapper.find(LTTP_INPUT);
      const earthboundInput = this.wrapper.find(EARTHBOUND_INPUT);
      const joustInput = this.wrapper.find(JOUST_INPUT);
      expect(lttpInput.length).to.equal(1);
      expect(earthboundInput.length).to.equal(1);
      expect(joustInput.length).to.equal(1);
      expect(this.wrapper.find('label').at(0)).to.have.text('A Link to the Past');
      expect(this.wrapper.find('label').at(1)).to.have.text('Earthbound');
      expect(this.wrapper.find('label').at(2)).to.have.text('Joust');
    });

    it('displays two inputs marked as optional', function (this: $TSFixMe) {
      expect(this.wrapper.find('TextInput').at(1).text()).to.include('Optional');
      expect(this.wrapper.find('TextInput').at(2).text()).to.include('Optional');
    });

    it('no input has a set value', function (this: $TSFixMe) {
      const lttpInput = this.wrapper.find(LTTP_INPUT);
      const earthboundInput = this.wrapper.find(EARTHBOUND_INPUT);
      const joustInput = this.wrapper.find(JOUST_INPUT);
      expect(lttpInput.prop('value')).to.equal('');
      expect(earthboundInput.prop('value')).to.equal('');
      expect(joustInput.prop('value')).to.equal('');
    });

    it('displays the endpoint radio buttons', function (this: $TSFixMe) {
      expect(this.wrapper.find(US_ENDPOINT_INPUT).length).to.equal(1);
      expect(this.wrapper.find(EU_ENDPOINT_INPUT).length).to.equal(1);
    });

    it('defaults to selecting the US radio button', function (this: $TSFixMe) {
      // eslint-disable-next-line @typescript-eslint/no-unused-expressions
      expect(this.wrapper.find(US_ENDPOINT_INPUT).prop('checked')).to.be.true;
      // eslint-disable-next-line @typescript-eslint/no-unused-expressions
      expect(this.wrapper.find(EU_ENDPOINT_INPUT).prop('checked')).to.not.be.true;
    });

    it('displays a test connect button', function (this: $TSFixMe) {
      expect(this.wrapper.find(TEST_BUTTON).length).to.equal(1);
    });

    it('does display the View Instructions for third party integrations link', function (this: $TSFixMe) {
      expect(this.wrapper.find(THIRD_PARTY_INTEG_DOC_LINK).length).to.equal(1);
    });

    it('does not display the standalone View Instructions for third party integrations link', function (this: $TSFixMe) {
      expect(this.wrapper.find(STANDALONE_THIRD_PARTY_INTEG_DOC_LINK).length).to.equal(0);
    });

    it('marks the test integration and submit buttons as disabled', function (this: $TSFixMe) {
      // eslint-disable-next-line @typescript-eslint/no-unused-expressions
      expect(this.wrapper.find(TEST_BUTTON).prop('aria-disabled')).to.be.true;
      // eslint-disable-next-line @typescript-eslint/no-unused-expressions
      expect(this.wrapper.find(SUBMIT_BUTTON).prop('aria-disabled')).to.be.true;
    });

    it('displays a close button', function (this: $TSFixMe) {
      expect(this.wrapper.find(CLOSE_BUTTON).length).to.equal(1);
      expect(this.wrapper.find(CLOSE_BUTTON).text()).to.equal('Close');
    });

    describe('when the form is filled out without changing the default region', function () {
      beforeEach(function (this: $TSFixMe) {
        this.wrapper.find(LTTP_INPUT).simulate('change', { target: { value: 'link' } });
        this.wrapper.find(EARTHBOUND_INPUT).simulate('change', { target: { value: 'ness' } });
        this.wrapper.find(JOUST_INPUT).simulate('change', { target: { value: 'knight' } });
        return Promise.resolve()
          .then()
          .then(() => this.wrapper.update());
      });

      it('marks the test integration and submit buttons as enabled', function (this: $TSFixMe) {
        // eslint-disable-next-line @typescript-eslint/no-unused-expressions
        expect(this.wrapper.find(TEST_BUTTON).prop('disabled')).to.not.be.true;
        // eslint-disable-next-line @typescript-eslint/no-unused-expressions
        expect(this.wrapper.find(SUBMIT_BUTTON).prop('disabled')).to.not.be.true;
      });
    });

    describe('when the form is filled out', function () {
      beforeEach(function (this: $TSFixMe) {
        this.wrapper.find(LTTP_INPUT).simulate('change', { target: { value: 'link' } });
        this.wrapper.find(EARTHBOUND_INPUT).simulate('change', { target: { value: 'ness' } });
        this.wrapper.find(JOUST_INPUT).simulate('change', { target: { value: 'knight' } });
        this.wrapper.find(EU_ENDPOINT_INPUT).simulate('change', { target: { value: 'EU' } });
        return Promise.resolve()
          .then()
          .then(() => this.wrapper.update());
      });

      it('marks the test integration and submit buttons as enabled', function (this: $TSFixMe) {
        // eslint-disable-next-line @typescript-eslint/no-unused-expressions
        expect(this.wrapper.find(TEST_BUTTON).prop('disabled')).to.not.be.true;
        // eslint-disable-next-line @typescript-eslint/no-unused-expressions
        expect(this.wrapper.find(SUBMIT_BUTTON).prop('disabled')).to.not.be.true;
      });

      describe('when clicking to test the integration', function () {
        beforeEach(function (this: $TSFixMe) {
          this.wrapper.find(TEST_BUTTON).simulate('click');
        });

        it('disables the form', function (this: $TSFixMe) {
          this.testFormEnabled(false);
        });

        it('calls to test the integration', function (this: $TSFixMe) {
          // eslint-disable-next-line @typescript-eslint/no-unused-expressions
          expect(this.testIntegrationStub).to.have.been.calledOnce;
        });

        describe('when the integration test is successful', function () {
          beforeEach(function (this: $TSFixMe) {
            this.testIntegrationDeferred.resolve();
            return Promise.resolve()
              .then()
              .then(() => this.wrapper.update());
          });

          it('displays a successful test message', function (this: $TSFixMe) {
            this.testAlert({ variant: 'success', header: 'Test Successful' });
          });

          it('reenables the form', function (this: $TSFixMe) {
            this.testFormEnabled(true);
          });
        });

        describe('when the integration test is unsuccessful', function () {
          beforeEach(function (this: $TSFixMe) {
            this.testErrorMessage = 'ganondorf';
            this.testIntegrationDeferred.reject({
              errorCode: 'TEST_INTEGRATION_ERROR',
              message: this.testErrorMessage,
            });
            return Promise.resolve()
              .then()
              .then(() => this.wrapper.update());
          });

          it('displays a test failure message', function (this: $TSFixMe) {
            this.testAlert({ variant: 'danger', header: 'Test Failed', message: this.testErrorMessage });
          });

          it('re-enables only the inputs', function (this: $TSFixMe) {
            this.testFormAfterError();
          });

          describe('when changing the value of an input', function () {
            beforeEach(function (this: $TSFixMe) {
              this.wrapper.find(LTTP_INPUT).simulate('change', { target: { value: 'farore' } });
            });

            it('re-enables the entire form', function (this: $TSFixMe) {
              this.testFormEnabled(true);
            });
          });
        });
      });

      describe('when clicking to submit the integration', function () {
        beforeEach(function (this: $TSFixMe) {
          this.configurationDeferred = new Deferred();
          this.configurationStub.returns(this.configurationDeferred.promise());
          this.wrapper.find(SUBMIT_BUTTON).simulate('click');
        });

        it('disables the form', function (this: $TSFixMe) {
          this.testFormEnabled(false);
        });

        it('calls to test the integration', function (this: $TSFixMe) {
          // eslint-disable-next-line @typescript-eslint/no-unused-expressions
          expect(this.configurationStub).to.have.been.calledOnce;
        });

        it('sends a tracking event', function (this: $TSFixMe) {
          expect(this.analyticsSpy).to.have.been.calledWith(SEGMENT_EVENTS.BUTTON_CLICKED, {
            context: `Configure ${this.commonProps.integrationName} Integration Modal`,
            action: 'Save Button Clicked',
            ...{ integration: this.commonProps.integrationName },
          });
        });

        describe('when the configuration is successful', function () {
          beforeEach(function (this: $TSFixMe) {
            this.configurationDeferred.resolve();
            return Promise.resolve()
              .then()
              .then(() => this.wrapper.update());
          });

          it('displays a successful test message', function (this: $TSFixMe) {
            this.testAlert({ variant: 'success', message: 'Successfully saved Super Nintendo configuration changes.' });
          });

          it('hides the form and submit button', function (this: $TSFixMe) {
            this.testFormInvisible();
          });
        });

        describe('when the configuration succeeds with a legacy 200 response', function () {
          beforeEach(function (this: $TSFixMe) {
            this.configurationDeferred.resolve({
              status: 'NONE',
            });
            return Promise.resolve()
              .then()
              .then(() => this.wrapper.update());
          });

          it('displays a successful test message', function (this: $TSFixMe) {
            this.testAlert({ variant: 'success', message: 'Successfully saved Super Nintendo configuration changes.' });
          });

          it('hides the form and submit button', function (this: $TSFixMe) {
            this.testFormInvisible();
          });
        });

        describe('when the configuration fails with a non-200 response', function () {
          beforeEach(function (this: $TSFixMe) {
            this.testErrorMessage = 'ganondorf';
            this.configurationDeferred.reject({
              errorCode: 'TEST_INTEGRATION_ERROR',
              message: this.testErrorMessage,
            });
            return Promise.resolve()
              .then()
              .then(() => this.wrapper.update());
          });

          it('displays a failure message', function (this: $TSFixMe) {
            this.testAlert({ variant: 'danger', message: this.testErrorMessage });
          });

          describe('when changing the value of an input', function () {
            beforeEach(function (this: $TSFixMe) {
              this.wrapper.find(LTTP_INPUT).simulate('change', { target: { value: 'farore' } });
            });

            it('re-enables the entire form', function (this: $TSFixMe) {
              this.testFormEnabled(true);
            });
          });
        });

        describe('when the configuration fails with a FORBIDDEN response', function () {
          beforeEach(function (this: $TSFixMe) {
            this.configurationDeferred.reject({
              status: 'ERROR',
              errorCode: 'FORBIDDEN',
              message: 'Additional permissions are required to access the requested resource.',
            });
            return Promise.resolve()
              .then()
              .then(() => this.wrapper.update());
          });

          it('displays a failure message', function (this: $TSFixMe) {
            this.testAlert({
              variant: 'danger',
              message: 'Additional permissions are required to access the requested resource.',
            });
          });
        });

        describe('when the configuration fails with a legacy 200 response', function () {
          beforeEach(function (this: $TSFixMe) {
            this.configurationDeferred.resolve({
              status: 'ERROR',
            });
            return Promise.resolve()
              .then()
              .then(() => this.wrapper.update());
          });

          it('displays a failure message', function (this: $TSFixMe) {
            this.testAlert({ variant: 'danger', message: 'not well formed' });
          });

          describe('when changing the value of an input', function () {
            beforeEach(function (this: $TSFixMe) {
              this.wrapper.find(LTTP_INPUT).simulate('change', { target: { value: 'farore' } });
            });

            it('re-enables the entire form', function (this: $TSFixMe) {
              this.testFormEnabled(true);
            });
          });
        });
      });

      describe('when clicking the close button', function () {
        beforeEach(function (this: $TSFixMe) {
          this.wrapper.find('.view-modal-body').find(CLOSE_BUTTON).simulate('click');
        });

        it('sends a tracking event', function (this: $TSFixMe) {
          expect(this.analyticsSpy).to.have.been.calledWith(SEGMENT_EVENTS.BUTTON_CLICKED, {
            context: `Configure ${this.commonProps.integrationName} Integration Modal`,
            action: 'Close Button Clicked',
          });
        });
      });
    });

    describe('when the form is filled out omitting an optional field', function () {
      beforeEach(function (this: $TSFixMe) {
        this.wrapper.find(LTTP_INPUT).simulate('change', { target: { value: 'link' } });
        this.wrapper.find(JOUST_INPUT).simulate('change', { target: { value: 'knight' } });
        this.wrapper.find(EU_ENDPOINT_INPUT).simulate('change', { target: { value: 'EU' } });
        return Promise.resolve()
          .then()
          .then(() => this.wrapper.update());
      });

      it('marks the test integration and submit buttons as enabled', function (this: $TSFixMe) {
        // eslint-disable-next-line @typescript-eslint/no-unused-expressions
        expect(this.wrapper.find(TEST_BUTTON).prop('disabled')).to.not.be.true;
        // eslint-disable-next-line @typescript-eslint/no-unused-expressions
        expect(this.wrapper.find(SUBMIT_BUTTON).prop('disabled')).to.not.be.true;
      });
    });

    describe('when the form is filled out omitting an optional field that is required for testing', function () {
      beforeEach(function (this: $TSFixMe) {
        this.wrapper.find(LTTP_INPUT).simulate('change', { target: { value: 'link' } });
        this.wrapper.find(EARTHBOUND_INPUT).simulate('change', { target: { value: 'ness' } });
        this.wrapper.find(EU_ENDPOINT_INPUT).simulate('change', { target: { value: 'EU' } });
        return Promise.resolve()
          .then()
          .then(() => this.wrapper.update());
      });

      it('marks the test integration disabled', function (this: $TSFixMe) {
        // eslint-disable-next-line @typescript-eslint/no-unused-expressions
        expect(this.wrapper.find(TEST_BUTTON).prop('aria-disabled')).to.be.true;
      });

      it('marks the submit button as enabled', function (this: $TSFixMe) {
        // eslint-disable-next-line @typescript-eslint/no-unused-expressions
        expect(this.wrapper.find(SUBMIT_BUTTON).prop('disabled')).to.not.be.true;
      });
    });

    describe('when the form is filled out omitting the non-optional field', function () {
      beforeEach(function (this: $TSFixMe) {
        this.wrapper.find(EARTHBOUND_INPUT).simulate('change', { target: { value: 'ness' } });
        this.wrapper.find(JOUST_INPUT).simulate('change', { target: { value: 'knight' } });
        this.wrapper.find(EU_ENDPOINT_INPUT).simulate('change', { target: { value: 'EU' } });
        return Promise.resolve()
          .then()
          .then(() => this.wrapper.update());
      });

      it('does not mark the test integration and submit buttons as enabled', function (this: $TSFixMe) {
        // eslint-disable-next-line @typescript-eslint/no-unused-expressions
        expect(this.wrapper.find(TEST_BUTTON).prop('aria-disabled')).to.be.true;
        // eslint-disable-next-line @typescript-eslint/no-unused-expressions
        expect(this.wrapper.find(SUBMIT_BUTTON).prop('aria-disabled')).to.be.true;
      });
    });
  });

  describe('when rendered with all possible options with initial values', function () {
    beforeEach(function (this: $TSFixMe) {
      const allProps = {
        ...this.commonProps,
        initialEndpoint: 'EU',
        testIntegration: this.testIntegrationStub,
        inputs: [
          {
            displayName: 'A Link to the Past',
            name: 'aLinkToThePast',
            initialValue: 'Zelda',
          },
          {
            displayName: 'Earthbound',
            name: 'earthbound',
            initialValue: 'Paula',
          },
          {
            displayName: 'Joust',
            name: 'joust',
            initialValue: 'Knight',
          },
          {
            displayName: 'VictorOps',
            name: 'victorOpsRoutingKey',
            initialValue: '1234',
          },
        ],
      };

      this.wrapper = this.renderComponent(allProps);
    });

    it('displays the correct modal title', function (this: $TSFixMe) {
      expect(this.wrapper.find(MODAL_TITLE).text()).to.equal('Super Nintendo Configuration');
    });

    it('displays each input with the expected initial value', function (this: $TSFixMe) {
      expect(this.wrapper.find(LTTP_INPUT).prop('value')).to.equal('Zelda');
      expect(this.wrapper.find(EARTHBOUND_INPUT).prop('value')).to.equal('Paula');
      expect(this.wrapper.find(JOUST_INPUT).prop('value')).to.equal('Knight');
      expect(this.wrapper.find(VICTOROPS_INPUT).prop('value')).to.equal('1234');
    });

    it('displays each input as disabled', function (this: $TSFixMe) {
      // eslint-disable-next-line @typescript-eslint/no-unused-expressions
      expect(this.wrapper.find(LTTP_INPUT).prop('disabled')).to.be.true;
      // eslint-disable-next-line @typescript-eslint/no-unused-expressions
      expect(this.wrapper.find(EARTHBOUND_INPUT).prop('disabled')).to.be.true;
      // eslint-disable-next-line @typescript-eslint/no-unused-expressions
      expect(this.wrapper.find(JOUST_INPUT).prop('disabled')).to.be.true;
      // eslint-disable-next-line @typescript-eslint/no-unused-expressions
      expect(this.wrapper.find(VICTOROPS_INPUT).prop('disabled')).to.be.true;
    });

    it('displays the radio buttons with an initial value', function (this: $TSFixMe) {
      // eslint-disable-next-line @typescript-eslint/no-unused-expressions
      expect(this.wrapper.find(US_ENDPOINT_INPUT).prop('checked')).to.not.be.true;
      // eslint-disable-next-line @typescript-eslint/no-unused-expressions
      expect(this.wrapper.find(EU_ENDPOINT_INPUT).prop('checked')).to.be.true;
    });

    it('displays the radio buttons as disabled', function (this: $TSFixMe) {
      // eslint-disable-next-line @typescript-eslint/no-unused-expressions
      expect(this.wrapper.find(US_ENDPOINT_INPUT).prop('disabled')).to.be.true;
    });

    it('marks the test integration button as enabled', function (this: $TSFixMe) {
      // eslint-disable-next-line @typescript-eslint/no-unused-expressions
      expect(this.wrapper.find(TEST_BUTTON).prop('disabled')).to.not.be.true;
    });

    it('displays an edit button', function (this: $TSFixMe) {
      expect(this.wrapper.find(EDIT_BUTTON).length).to.equal(1);
    });

    it('displays a close button', function (this: $TSFixMe) {
      expect(this.wrapper.find(CLOSE_BUTTON).length).to.equal(1);
      expect(this.wrapper.find(CLOSE_BUTTON).text()).to.equal('Close');
    });

    it('does display the View Instructions for third party integrations link', function (this: $TSFixMe) {
      expect(this.wrapper.find(THIRD_PARTY_INTEG_DOC_LINK).length).to.equal(1);
    });

    it('does not display the standalone View Instructions for third party integrations link', function (this: $TSFixMe) {
      expect(this.wrapper.find(STANDALONE_THIRD_PARTY_INTEG_DOC_LINK).length).to.equal(0);
    });

    describe('when the edit button is clicked', function () {
      beforeEach(function (this: $TSFixMe) {
        this.wrapper.find(EDIT_BUTTON).simulate('click');
      });

      it('displays the correct modal title', function (this: $TSFixMe) {
        expect(this.wrapper.find(MODAL_TITLE).text()).to.equal('Edit Super Nintendo Configuration');
      });

      it('clears input fields', function (this: $TSFixMe) {
        expect(this.wrapper.find(LTTP_INPUT).prop('value')).to.equal('');
        expect(this.wrapper.find(EARTHBOUND_INPUT).prop('value')).to.equal('');
        expect(this.wrapper.find(JOUST_INPUT).prop('value')).to.equal('');
      });

      it('does not clear VictorOps routing key field', function (this: $TSFixMe) {
        expect(this.wrapper.find(VICTOROPS_INPUT).prop('value')).to.equal('1234');
      });

      it('displays each input as enabled', function (this: $TSFixMe) {
        // eslint-disable-next-line @typescript-eslint/no-unused-expressions
        expect(this.wrapper.find(LTTP_INPUT).prop('disabled')).to.not.be.true;
        // eslint-disable-next-line @typescript-eslint/no-unused-expressions
        expect(this.wrapper.find(EARTHBOUND_INPUT).prop('disabled')).to.not.be.true;
        // eslint-disable-next-line @typescript-eslint/no-unused-expressions
        expect(this.wrapper.find(JOUST_INPUT).prop('disabled')).to.not.be.true;
        // eslint-disable-next-line @typescript-eslint/no-unused-expressions
        expect(this.wrapper.find(VICTOROPS_INPUT).prop('disabled')).to.not.be.true;
      });

      it('displays the radio buttons as enabled', function (this: $TSFixMe) {
        // eslint-disable-next-line @typescript-eslint/no-unused-expressions
        expect(this.wrapper.find(US_ENDPOINT_INPUT).prop('disabled')).to.not.be.true;
      });

      it('displays the test integration button as disabled', function (this: $TSFixMe) {
        // eslint-disable-next-line @typescript-eslint/no-unused-expressions
        expect(this.wrapper.find(TEST_BUTTON).prop('aria-disabled')).to.be.true;
      });

      it('displays a save button', function (this: $TSFixMe) {
        expect(this.wrapper.find(SUBMIT_BUTTON).length).to.equal(1);
      });

      it('displays a cancel button', function (this: $TSFixMe) {
        expect(this.wrapper.find(CLOSE_BUTTON).length).to.equal(1);
        expect(this.wrapper.find(CLOSE_BUTTON).text()).to.equal('Cancel');
      });

      describe('when the form is filled out', function () {
        beforeEach(function (this: $TSFixMe) {
          this.wrapper.find(LTTP_INPUT).simulate('change', { target: { value: 'link' } });
          this.wrapper.find(EARTHBOUND_INPUT).simulate('change', { target: { value: 'ness' } });
          this.wrapper.find(JOUST_INPUT).simulate('change', { target: { value: 'knight' } });
          this.wrapper.find(EU_ENDPOINT_INPUT).simulate('change', { target: { value: 'EU' } });
          return Promise.resolve()
            .then()
            .then(() => this.wrapper.update());
        });

        it('marks the test integration and submit buttons as enabled', function (this: $TSFixMe) {
          // eslint-disable-next-line @typescript-eslint/no-unused-expressions
          expect(this.wrapper.find(TEST_BUTTON).prop('disabled')).to.not.be.true;
          // eslint-disable-next-line @typescript-eslint/no-unused-expressions
          expect(this.wrapper.find(SUBMIT_BUTTON).prop('disabled')).to.not.be.true;
        });

        describe('when clicking to submit the integration', function () {
          beforeEach(function (this: $TSFixMe) {
            this.configurationDeferred = new Deferred();
            this.configurationStub.returns(this.configurationDeferred.promise());
            this.wrapper.find(SUBMIT_BUTTON).simulate('click');
          });

          it('disables the form', function (this: $TSFixMe) {
            this.testFormEnabled(false);
          });

          it('calls to test the integration', function (this: $TSFixMe) {
            // eslint-disable-next-line @typescript-eslint/no-unused-expressions
            expect(this.configurationStub).to.have.been.calledOnce;
          });

          it('sends a tracking event', function (this: $TSFixMe) {
            expect(this.analyticsSpy).to.have.been.calledWith(SEGMENT_EVENTS.BUTTON_CLICKED, {
              context: `Configure ${this.commonProps.integrationName} Integration Modal`,
              action: 'Save Button Clicked',
              ...{ integration: this.commonProps.integrationName },
            });
          });

          describe('when the configuration is successful', function () {
            beforeEach(function (this: $TSFixMe) {
              this.configurationDeferred.resolve();
              return Promise.resolve()
                .then()
                .then(() => this.wrapper.update());
            });

            it('displays the correct modal title', function (this: $TSFixMe) {
              expect(this.wrapper.find(MODAL_TITLE).text()).to.equal('Super Nintendo Configuration');
            });

            it('displays a successful test message', function (this: $TSFixMe) {
              this.testAlert({
                variant: 'success',
                message: 'Successfully saved Super Nintendo configuration changes.',
              });
            });

            it('hides the form and submit button', function (this: $TSFixMe) {
              this.testFormInvisible();
            });
          });
        });

        describe('when clicking the close button', function () {
          beforeEach(function (this: $TSFixMe) {
            this.wrapper.find('.view-modal-body').find(CLOSE_BUTTON).simulate('click');
          });

          it('sends a tracking event', function (this: $TSFixMe) {
            expect(this.analyticsSpy).to.have.been.calledWith(SEGMENT_EVENTS.BUTTON_CLICKED, {
              context: `Configure ${this.commonProps.integrationName} Integration Modal`,
              action: 'Close Button Clicked',
            });
          });
        });
      });
    });
  });

  describe('when rendered with a deprecation message', function () {
    beforeEach(function (this: $TSFixMe) {
      const allProps = {
        ...this.commonProps,
        inputs: [
          {
            displayName: 'A Link to the Past',
            name: 'aLinkToThePast',
          },
        ],
        deprecationMessage: <p data-testid="test-deprecation-message">This integration is deprecated</p>,
      };

      this.wrapper = this.renderComponent(allProps);
    });

    it('renders the deprecation message', function (this: $TSFixMe) {
      // eslint-disable-next-line @typescript-eslint/no-unused-expressions
      expect(this.wrapper.find("[data-testid='test-deprecation-message']")).to.exist;
    });

    it('renders with the test button disabled', function (this: $TSFixMe) {
      // eslint-disable-next-line @typescript-eslint/no-unused-expressions
      expect(this.wrapper.find(TEST_BUTTON).prop('aria-disabled')).to.be.true;
    });

    it('renders with the inputs disabled', function (this: $TSFixMe) {
      // eslint-disable-next-line @typescript-eslint/no-unused-expressions
      expect(this.wrapper.find(LTTP_INPUT).prop('disabled')).to.be.true;
    });

    it('renders with the submit button disabled', function (this: $TSFixMe) {
      // eslint-disable-next-line @typescript-eslint/no-unused-expressions
      expect(this.wrapper.find(SUBMIT_BUTTON).prop('aria-disabled')).to.be.true;
    });

    it('does display the View Instructions for third party integrations link', function (this: $TSFixMe) {
      expect(this.wrapper.find(THIRD_PARTY_INTEG_DOC_LINK).length).to.equal(1);
    });

    it('does not display the standalone View Instructions for third party integrations link', function (this: $TSFixMe) {
      expect(this.wrapper.find(STANDALONE_THIRD_PARTY_INTEG_DOC_LINK).length).to.equal(0);
    });
  });

  describe('when rendered with all possible options with initial values without showing test integration', function () {
    beforeEach(function (this: $TSFixMe) {
      const allProps = {
        ...this.commonProps,
        showTestIntegration: false,
        inputs: [
          {
            displayName: 'A Link to the Past',
            name: 'aLinkToThePast',
          },
          {
            displayName: 'Earthbound',
            name: 'earthbound',
            optional: true,
          },
          {
            displayName: 'Joust',
            name: 'joust',
            optional: true,
            requiredForTest: true,
          },
        ],
      };

      this.wrapper = this.renderComponent(allProps);
    });

    it('does not display the View Instructions for third party integrations link', function (this: $TSFixMe) {
      expect(this.wrapper.find(THIRD_PARTY_INTEG_DOC_LINK).length).to.equal(0);
    });

    it('does display the standalone View Instructions for third party integrations link', function (this: $TSFixMe) {
      expect(this.wrapper.find(STANDALONE_THIRD_PARTY_INTEG_DOC_LINK).length).to.equal(1);
    });
  });
});
