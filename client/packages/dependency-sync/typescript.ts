import fs from 'fs';
import { applyEdits, modify } from 'jsonc-parser';
import path from 'path';

import { getPackageDirectory } from './bazel';

interface Reference {
  path: string;
}

export function updateTsConfigWithReferences(packageName: string, dependencies: Array<string>) {
  const references = dependencies
    .filter((dependency) => isPackageDependency(dependency))
    .map((dependency) => translateDependencyToReference(packageName, dependency))
    .sort((a, b) => a.path.localeCompare(b.path));

  const tsConfigPath = getTsConfigPath(packageName);

  writeReferences(tsConfigPath, references);

  return { tsConfigPath };
}

function getTsConfigPath(packageName: string): string {
  return path.join(getPackageDirectory(packageName), 'tsconfig.json');
}

function writeReferences(tsConfigPath: string, references: Array<Reference>) {
  const inputJson = fs.readFileSync(tsConfigPath).toString();

  const formattingOptions = { tabSize: 2, insertSpaces: true };
  const outputJson = applyEdits(inputJson, modify(inputJson, ['references'], references, { formattingOptions }));

  fs.writeFileSync(tsConfigPath, outputJson);
}

function translateDependencyToReference(packageName: string, dependency: string): Reference {
  const referencePath = path.relative(getPackageDirectory(packageName), getPackageDirectory(dependency));
  return { path: referencePath };
}

function isPackageDependency(dependency: string): boolean {
  return (
    dependency.startsWith('//client/packages/') ||
    dependency.startsWith('//client/apps/') ||
    dependency.startsWith('//client/scripts/')
  );
}
