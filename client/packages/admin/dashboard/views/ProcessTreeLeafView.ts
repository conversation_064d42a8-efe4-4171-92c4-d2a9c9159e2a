// @ts-expect-error TS(7016): Could not find a declaration file for module 'back... Remove this comment to see the full error message
import Marion<PERSON> from 'backbone.marionette';
import _ from 'underscore';

import * as hostHelper from '@packages/legacy/auto/helpers/hostHelper';
import tooltips from '@packages/common/utils/mixins/tooltips';
import mixInto from '@packages/common/utils/mixInto';

// templates
import template from './processTreeLeafTemplate.hbs';

const ProcessTreeLeafView = mixInto(Marionette.View)(tooltips).extend({
  template,
  tagName: 'tr',
  className: 'tree-table-row',
  templateContext() {
    return {
      hostnameAndPort: this.model.getHostnameAndPort(),
      hasLastPing: this.model.getState().hasLastPing(),
      depth: _.range(this._getDepth()),
      // @ts-expect-error TS(2554): Expected 2 arguments, but got 1.
      hostIcon: hostHelper.hostTypeIcon(this.model),
    };
  },

  _getDepth() {
    if (this.model.isTopLevelItem()) {
      return 1;
    }
    if (this.model.isMongos() || this.model.isConfigServer() || !this.model.getClusterName()) {
      return 2;
    }
    return 3;
  },
});

export default ProcessTreeLeafView;
