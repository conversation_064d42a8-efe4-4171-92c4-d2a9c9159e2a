import { Component } from 'react';

import Button, { Size as ButtonSize } from '@leafygreen-ui/button';
import Icon from '@leafygreen-ui/icon';
import { Menu, MenuItem } from '@leafygreen-ui/menu';
import { connect } from 'react-redux';

import { Role } from '@packages/types/roles';
import { WithDefaultProps } from '@packages/types/withDefaultProps';

import * as viewer from '@packages/redux/common/viewer';

import { accessManagerTablesStyles } from '@packages/common/styles/accessManager';
import { truncatePrivateKey } from '@packages/common/utils/apiUserHelpers';
import ApiKeyDetailsModal from '@packages/components/Access/ApiKeyDetailsModal';
import { DeleteModal } from '@packages/components/DeleteModal';
import PermissionsDropdown from '@packages/components/PermissionsDropdown';
import Table from '@packages/components/Table';

interface OwnProps {
  apiUsers?: Array<$TSFixMe>;
  activeGroupId: string;
  onApiUserRolesChange: $TSFixMeFunction;
  onApiUserDelete: $TSFixMeFunction;
  isGroupOwner: boolean;
  allowKeyManagement?: boolean;
}

type State = $TSFixMe;

const apiKeysTableDefaultProps = {
  apiUsers: [],
  allowKeyManagement: false,
};

type Props = WithDefaultProps<OwnProps, typeof apiKeysTableDefaultProps>;

class ApiKeysTable extends Component<Props, State> {
  static defaultProps = apiKeysTableDefaultProps;

  state = {
    editingUserId: null,
    pendingRoles: null,
    showLoading: null,
    deletingApiUser: null,
    detailsModalApiUsername: '',
  };

  onDeleteButtonClick = (deletingApiUser: $TSFixMe) => {
    this.setState({
      deletingApiUser,
    });
  };

  onSubmitDelete = () => {
    const { onApiUserDelete } = this.props;
    const { deletingApiUser } = this.state;
    // @ts-expect-error ts-migrate(2531) FIXME: Object is possibly 'null'.
    return onApiUserDelete(deletingApiUser.userId);
  };

  openDetailsModal = ({ username }: { username: string }) => {
    this.setState({
      detailsModalApiUsername: username,
    });
  };

  onCloseDetailsModal = () => {
    this.setState({
      detailsModalApiUsername: '',
    });
  };

  saveRoles = async () => {
    const { onApiUserRolesChange } = this.props;
    const { editingUserId, pendingRoles } = this.state;
    this.setState({
      rolesErrorCode: null,
      showLoading: true,
    });

    try {
      await onApiUserRolesChange(editingUserId, pendingRoles);

      this.setState({
        editingUserId: null,
        pendingRoles: null,
        showLoading: false,
      });
    } catch (e) {
      this.setState({
        rolesErrorCode: e.errorCode,
        showLoading: false,
      });
    }
  };

  cancelSaveRoles = () => {
    this.setState({
      editingUserId: null,
      rolesErrorCode: null,
    });
  };

  handleRolesChange = (newVals: Array<Role>) => {
    this.setState({ pendingRoles: newVals });
  };

  getRolesCell = ({ data: { userId }, value }: { data: { userId: string }; value: string }) => {
    const { editingUserId } = this.state;
    if (editingUserId == null || userId !== editingUserId) {
      return <Table.Cell key="roles">{value}</Table.Cell>;
    }

    // @ts-expect-error ts-migrate(2339) FIXME: Property 'rolesErrorCode' does not exist on type '... Remove this comment to see the full error message
    const { rolesErrorCode, showLoading, pendingRoles } = this.state;

    return (
      <Table.Cell key="roles">
        <div className="users-page-select-user-permissions-buttons">
          <PermissionsDropdown
            className="users-page-select-user-permissions"
            errorCode={rolesErrorCode}
            onChange={this.handleRolesChange}
            // @ts-expect-error ts-migrate(2322) FIXME: Type 'null' is not assignable to type 'readonly Ro... Remove this comment to see the full error message
            permissions={pendingRoles}
            type="group"
          />
          <button
            type="button"
            className="button users-page-button-has-padding-right button-is-primary"
            name="saveRolesButton"
            onClick={this.saveRoles}
            // @ts-expect-error ts-migrate(2322) FIXME: Type 'null' is not assignable to type 'boolean | u... Remove this comment to see the full error message
            disabled={showLoading}
          >
            <i className="fa fa-check" />
          </button>
          <button type="button" className="button" name="cancelSaveRolesButton" onClick={this.cancelSaveRoles}>
            <i className="fa fa-times" />
          </button>
          {showLoading && <span className="loading-spinner loading-spinner-is-select users-page-is-padded" />}
        </div>
      </Table.Cell>
    );
  };

  getEditAccessListMenuItem = (apiUser: { username: string }) => {
    const { username } = apiUser;
    const accessListHref = `#/access/apiKeys/edit/${username}/2`;

    return (
      <MenuItem
        key="editAccessList"
        data-testid="editAccessList"
        onClick={() => window.location.assign(accessListHref)}
      >
        Edit Access List
      </MenuItem>
    );
  };

  getMenuItems = (selectedApiUser: { username: string }) => {
    const { username } = selectedApiUser;
    const permissionsHref = `#/access/apiKeys/edit/${username}/1`;
    return [
      <MenuItem
        key="editPermissions"
        data-testid="editPermissions"
        onClick={() => window.location.assign(permissionsHref)}
      >
        Edit Permissions
      </MenuItem>,
      this.getEditAccessListMenuItem(selectedApiUser),
      <MenuItem key="viewDetails" data-testid="viewDetails" onClick={() => this.openDetailsModal(selectedApiUser)}>
        View Details
      </MenuItem>,
    ];
  };

  render() {
    const { apiUsers, allowKeyManagement, activeGroupId } = this.props;
    const { deletingApiUser, detailsModalApiUsername } = this.state;

    const tableData = apiUsers.map((apiUser) => {
      const groupInfo = apiUser.groupRoles.filter(
        (groupRole: { groupId: string }) => groupRole.groupId === activeGroupId
      )[0];
      const { roles, rolesString } = groupInfo;
      return {
        ...apiUser,
        rolesString,
        roles,
      };
    });

    return (
      <>
        <Table data={tableData}>
          <Table.Column header="Public Key" key="publicKey" accessor="username" sortable />
          <Table.Column
            header="Private Key"
            accessor="privateKeyRedacted"
            sortable
            cell={({ value }) => <Table.Cell key="privateKeyRedacted">{truncatePrivateKey(value)}</Table.Cell>}
          />
          <Table.Column
            header="Description"
            className="access-page-td-break-word"
            accessor="desc"
            sortable
            cell={(props) => (
              <Table.Cell key="desc" className="access-page-td-break-word">
                {props.value}
              </Table.Cell>
            )}
          />
          <Table.Column header="Project Permissions" sortable accessor="rolesString" cell={this.getRolesCell} />
          <Table.Column
            header="Projects"
            sortable
            accessor="groupRoles"
            cell={({ value }) => (
              <Table.Cell key="projects">
                <div>{value.length}</div>
              </Table.Cell>
            )}
          />
          {allowKeyManagement && (
            <Table.Column
              header="Actions"
              cell={(props) => (
                <Table.Cell key="actions">
                  <div className="users-page-action-row">
                    <Menu
                      align="bottom"
                      justify="start"
                      trigger={
                        <Button
                          size={ButtonSize.XSmall}
                          css={accessManagerTablesStyles.ellipsisActionButton}
                          data-testid="apiKeyActions"
                          leftGlyph={<Icon glyph="Ellipsis" />}
                        />
                      }
                      renderMode="portal"
                    >
                      {this.getMenuItems(props.data)}
                    </Menu>
                    <Button
                      size="xsmall"
                      css={accessManagerTablesStyles.actionButton}
                      name="deleteApiKey"
                      data-testid="delete-api-key-button"
                      onClick={() => this.onDeleteButtonClick(props.data)}
                      leftGlyph={<Icon glyph="Trash" size={12} />}
                    />
                  </div>
                </Table.Cell>
              )}
            />
          )}
        </Table>
        <DeleteModal
          title="Delete API Key"
          name=""
          warningContent={
            deletingApiUser
              ? `${
                  (deletingApiUser as $TSFixMe).username
                } will lose access to the project. This operation cannot be undone.`
              : ''
          }
          showConfirmationQuestion={false}
          onClose={() => this.setState({ deletingApiUser: null })}
          onDelete={this.onSubmitDelete}
          open={deletingApiUser != null}
          deleteLabel="Delete"
        />
        {detailsModalApiUsername && (
          <ApiKeyDetailsModal
            apiUsername={detailsModalApiUsername}
            showProjectDetails={false}
            onClose={this.onCloseDetailsModal}
            appContext="group"
          />
        )}
      </>
    );
  }
}

export default connect((state) => ({
  isGroupOwner: viewer.isGroupOwner(state),
}))(ApiKeysTable);

export { ApiKeysTable };
