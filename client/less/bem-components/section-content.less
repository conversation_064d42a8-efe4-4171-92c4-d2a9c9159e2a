.section-content {
  padding: 8px 0 15px 0;

  /*
        These margins align the content with the first tab of the section header.
    */
  &-has-wide-margins {
    padding: 27px 0 24px 0;
  }

  &-has-horizontal-overflow {
    overflow-x: auto;
    width: 100%;
    display: table;
  }

  &-has-table {
    padding-top: 0;
  }

  &-has-no-top-bottom-padding {
    padding-bottom: 0;
    padding-top: 0;
  }

  &-has-full-top-margin {
    padding: 15px;
  }

  &-has-small-bottom-margin {
    padding-bottom: 10px;
  }

  // for text-heavy content that should not indefinitely expand horizontally
  &-is-bounded {
    max-width: 700px;
  }

  &-full-height {
    height: 100%;
  }
}
