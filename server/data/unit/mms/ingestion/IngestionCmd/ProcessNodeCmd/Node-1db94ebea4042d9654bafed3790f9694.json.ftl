{
  "journalingEnabled": false,
  "preferredHostId": null,
  "preferredHostnameAndPort": null,
  "lastUptime": null,
  "lastMongosPing": {"$date": ${localTime?c}},
  "allHostIds": [
    "1db94ebea4042d9654bafed3790f9694",
    "2024813d3cd0b72696176a203835ecaf",
    "6e810d30907f7ffb588bdeb4e014535b",
    "714843d39fec563191375f9bb154d962",
    "caba46569f2c256478027d9dc5a838da"
  ],
  "compressedLocks": [
    {
      "process": "ip-10-165-19-246:30003:1375061801:1636004565",
      "ts": {"$oid": "51fca929bc57e8e546da1771"},
      "_id": "music.tweets",
      "when": {"$date": 1375512873230},
      "state": 0,
      "why": "split-{ reverse_tweet_id: \"166222921258902063\" }",
      "who": "ip-10-165-19-246:30003:1375061801:1636004565:conn27626:1321869984"
    },
    {
      "process": "ip-10-165-19-246:30000:1374715456:1804289383",
      "ts": {"$oid": "520d0109d9e6ba4252f0cafa"},
      "_id": "balancer",
      "when": {"$date": 1376583945575},
      "state": 0,
      "why": "doing balance round",
      "who": "ip-10-165-19-246:30000:1374715456:1804289383:Balancer:1681692777"
    },
    {
      "process": "ip-10-165-19-246:30000:1374715456:1804289383",
      "ts": {"$oid": "51f07e40d9e6ba4252edf775"},
      "_id": "configUpgrade",
      "when": {"$date": 1374715456863},
      "state": 0,
      "why": "upgrading config database to new format v4",
      "who": "ip-10-165-19-246:30000:1374715456:1804289383:mongosMain:846930886"
    }
  ],
  "hostIds": [
    "6e810d30907f7ffb588bdeb4e014535b",
    "caba46569f2c256478027d9dc5a838da",
    "714843d39fec563191375f9bb154d962"
  ],
  "replicaSetIds": [
    "treble",
    "bass"
  ],
  "isSelfId": "51f07e42d9e6ba4252edf77b",
  "version": "2.4.3",
  "replicaStates": [],
  "bits": "64",
  "lastHeartbeats": [{"$date": ${localTime?c}}],
  "startupWarnings": false,
  "shardIds": [
    "treble",
    "bass"
  ],
  "hostTypes": [2],
  "gitVersion": "fe1743177a5ea03e91e0052fb5e2cb2945f6d95f",
  "replicaHealth": [],
  "replicaMemberStates": [],
  "sysInfo": "Linux ip-10-2-29-40 ********-2.ec2.v1.2.fc8xen #1 SMP Fri Nov 20 17:48:28 EST 2009 x86_64 BOOST_LIB_VERSION=1_49",
  "hostnamesAndPorts": [
    "ec2-75-101-238-215.compute-1.amazonaws.com:30000",
    "*************:30000",
    "ip-10-165-19-246:30000"
  ],
  "lowUlimit": true,
  "uptime": 1873005,
  "groupId": "51ea93e1bb48184d7c86e3e5",
  "peers": [
    {
      "preferredHostId": null,
      "preferredHostnameAndPort": null,
      "lastUptime": null,
      "journalingEnabled": false,
      "allHostIds": [
        "17cae479fc196a69489e12e6caa6a89f",
        "6aa0a7750d577609d11b400743e12f27",
        "a5d8a97b8595a2789a9b20053997a4fb",
        "adb1388fad56dc80c4cec4b0c8ce7e8c",
        "ce0a90a14b972fb1e4afb6540044f2a4"
      ],
      "compressedLocks": null,
      "hostIds": [
        "adb1388fad56dc80c4cec4b0c8ce7e8c",
        "17cae479fc196a69489e12e6caa6a89f",
        "6aa0a7750d577609d11b400743e12f27"
      ],
      "replicaSetIds": ["treble"],
      "isSelfId": "51f07d197e74d904995702ac",
      "version": "2.4.3",
      "replicaStates": ["PRIMARY"],
      "bits": "64",
      "lastHeartbeats": [{"$date": 0}],
      "startupWarnings": false,
      "shardIds": ["treble"],
      "hostTypes": [7],
      "gitVersion": "fe1743177a5ea03e91e0052fb5e2cb2945f6d95f",
      "replicaHealth": [1],
      "replicaMemberStates": [1],
      "sysInfo": "Linux ip-10-2-29-40 ********-2.ec2.v1.2.fc8xen #1 SMP Fri Nov 20 17:48:28 EST 2009 x86_64 BOOST_LIB_VERSION=1_49",
      "hostnamesAndPorts": [
        "ec2-75-101-238-215.compute-1.amazonaws.com:30001",
        "*************:30001",
        "ip-10-165-19-246:30001"
      ],
      "lowUlimit": false,
      "uptime": 1909095,
      "groupId": "51ea93e1bb48184d7c86e3e5",
      "localTime": {"$date": ${localTime?c}},
      "compressedCmdLineOpts": {
        "parsed": {
          "port": 30001,
          "nojournal": "True",
          "logappend": "True",
          "dbpath": "/data/treble1",
          "config": "mongodb-cluster-treble-1.conf",
          "rest": "True",
          "oplogSize": 100,
          "smallfiles": "True",
          "replSet": "treble",
          "logpath": "/data/treble1/mongodb.log"
        },
        "argv": [
          "mongod",
          "--config",
          "mongodb-cluster-treble-1.conf"
        ],
        "ok": 1
      },
      "normalizedHostnamesAndPorts": [
        "IP-10-165-19-246:30001",
        "*************:30001",
        "EC2-75-101-238-215.COMPUTE-1.AMAZONAWS.COM:30001"
      ],
      "finalHostType": null
    },
    {
      "preferredHostId": null,
      "preferredHostnameAndPort": null,
      "lastUptime": null,
      "journalingEnabled": false,
      "allHostIds": [
        "19f5c86429183f03ead76e9082b4316c",
        "8b40882520a6fb489e0beaf0706a2ced",
        "cf2dc0884a2e2b9887b8b239530fd973",
        "d8ccddd580fc5a1fe464c6cb1e9a7ea2",
        "eb3c45c9a955e98f1c08c34ab439879f"
      ],
      "compressedLocks": null,
      "hostIds": [
        "cf2dc0884a2e2b9887b8b239530fd973",
        "eb3c45c9a955e98f1c08c34ab439879f",
        "d8ccddd580fc5a1fe464c6cb1e9a7ea2"
      ],
      "replicaSetIds": ["bass"],
      "isSelfId": "51f07d1ba1dd897d39c81064",
      "version": "2.4.3",
      "replicaStates": ["SECONDARY"],
      "bits": "64",
      "lastHeartbeats": [{"$date": ${localTime?c}}],
      "startupWarnings": false,
      "shardIds": ["bass"],
      "hostTypes": [8],
      "gitVersion": "fe1743177a5ea03e91e0052fb5e2cb2945f6d95f",
      "replicaHealth": [1],
      "replicaMemberStates": [2],
      "sysInfo": "Linux ip-10-2-29-40 ********-2.ec2.v1.2.fc8xen #1 SMP Fri Nov 20 17:48:28 EST 2009 x86_64 BOOST_LIB_VERSION=1_49",
      "hostnamesAndPorts": [
        "ec2-54-227-152-13.compute-1.amazonaws.com:30004",
        "**************:30004",
        "ip-10-154-139-202:30004"
      ],
      "lowUlimit": false,
      "uptime": 1909022,
      "groupId": "51ea93e1bb48184d7c86e3e5",
      "localTime": {"$date": ${localTime?c}},
      "compressedCmdLineOpts": {
        "parsed": {
          "port": 30004,
          "nojournal": "True",
          "logappend": "True",
          "dbpath": "/data/bass2",
          "config": "mongodb-cluster-bass-2.conf",
          "rest": "True",
          "oplogSize": 100,
          "smallfiles": "True",
          "replSet": "bass",
          "logpath": "/data/bass2/mongodb.log"
        },
        "argv": [
          "mongod",
          "--config",
          "mongodb-cluster-bass-2.conf"
        ],
        "ok": 1
      },
      "normalizedHostnamesAndPorts": [
        "**************:30004",
        "EC2-54-227-152-13.COMPUTE-1.AMAZONAWS.COM:30004",
        "IP-10-154-139-202:30004"
      ],
      "finalHostType": null
    },
    {
      "preferredHostId": null,
      "preferredHostnameAndPort": null,
      "lastUptime": null,
      "journalingEnabled": false,
      "allHostIds": [
        "229ef893d8f9b069757b5f212417422f",
        "4b0b9a45791be9d6fd0d70be2893df1d",
        "72482fc1dd5f1fd33a3bef016d864031",
        "a9d7173c3d7d606be498e7917c7d56bb",
        "c7793ee80e3e7a563111aebb13d9d733"
      ],
      "compressedLocks": null,
      "hostIds": [
        "4b0b9a45791be9d6fd0d70be2893df1d",
        "a9d7173c3d7d606be498e7917c7d56bb",
        "229ef893d8f9b069757b5f212417422f"
      ],
      "replicaSetIds": ["bass"],
      "isSelfId": "51f07d19bc57e8e546da1767",
      "version": "2.4.3",
      "replicaStates": ["PRIMARY"],
      "bits": "64",
      "lastHeartbeats": [{"$date": ${localTime?c}}],
      "startupWarnings": false,
      "shardIds": ["bass"],
      "hostTypes": [7],
      "gitVersion": "fe1743177a5ea03e91e0052fb5e2cb2945f6d95f",
      "replicaHealth": [1],
      "replicaMemberStates": [1],
      "sysInfo": "Linux ip-10-2-29-40 ********-2.ec2.v1.2.fc8xen #1 SMP Fri Nov 20 17:48:28 EST 2009 x86_64 BOOST_LIB_VERSION=1_49",
      "hostnamesAndPorts": [
        "ec2-75-101-238-215.compute-1.amazonaws.com:30003",
        "*************:30003",
        "ip-10-165-19-246:30003"
      ],
      "lowUlimit": false,
      "uptime": 1909090,
      "groupId": "51ea93e1bb48184d7c86e3e5",
      "localTime": {"$date": ${localTime?c}},
      "compressedCmdLineOpts": {
        "parsed": {
          "port": 30003,
          "nojournal": "True",
          "logappend": "True",
          "dbpath": "/data/bass1",
          "config": "mongodb-cluster-bass-1.conf",
          "rest": "True",
          "oplogSize": 100,
          "smallfiles": "True",
          "replSet": "bass",
          "logpath": "/data/bass1/mongodb.log"
        },
        "argv": [
          "mongod",
          "--config",
          "mongodb-cluster-bass-1.conf"
        ],
        "ok": 1
      },
      "normalizedHostnamesAndPorts": [
        "IP-10-165-19-246:30003",
        "*************:30003",
        "EC2-75-101-238-215.COMPUTE-1.AMAZONAWS.COM:30003"
      ],
      "finalHostType": null
    },
    {
      "preferredHostId": null,
      "preferredHostnameAndPort": null,
      "lastUptime": null,
      "journalingEnabled": false,
      "allHostIds": [
        "028cc3a488ecfa5b6cc24deb8f3b59ef",
        "74062ccbdf81bd587029e0d8c08e2f15",
        "a38e7e75b5b0c72f9ee60e88c861b41f",
        "a6729b13b15b352ecf945828946e2fc1",
        "e96981612023729d459f856985167ddf"
      ],
      "compressedLocks": null,
      "hostIds": [
        "e96981612023729d459f856985167ddf",
        "a6729b13b15b352ecf945828946e2fc1",
        "a38e7e75b5b0c72f9ee60e88c861b41f"
      ],
      "replicaSetIds": [],
      "isSelfId": "51f07e42079948b8be4e44d4",
      "version": "2.4.3",
      "replicaStates": [],
      "bits": "64",
      "lastHeartbeats": [],
      "startupWarnings": false,
      "shardIds": [],
      "hostTypes": [3],
      "gitVersion": "fe1743177a5ea03e91e0052fb5e2cb2945f6d95f",
      "replicaHealth": [],
      "replicaMemberStates": [],
      "sysInfo": "Linux ip-10-2-29-40 ********-2.ec2.v1.2.fc8xen #1 SMP Fri Nov 20 17:48:28 EST 2009 x86_64 BOOST_LIB_VERSION=1_49",
      "hostnamesAndPorts": [
        "ec2-75-101-238-215.compute-1.amazonaws.com:30005",
        "*************:30005",
        "ip-10-165-19-246:30005"
      ],
      "lowUlimit": true,
      "uptime": 1873040,
      "groupId": "51ea93e1bb48184d7c86e3e5",
      "localTime": {"$date": ${localTime?c}},
      "compressedCmdLineOpts": {
        "parsed": {
          "port": 30005,
          "nojournal": "True",
          "logappend": "True",
          "dbpath": "/data/config",
          "configsvr": "True",
          "config": "mongodb-cluster-config.conf",
          "rest": "True",
          "oplogSize": 100,
          "smallfiles": "True",
          "logpath": "/data/config/mongodb.log"
        },
        "argv": [
          "mongod",
          "--config",
          "mongodb-cluster-config.conf"
        ],
        "ok": 1
      },
      "normalizedHostnamesAndPorts": [
        "*************:30005",
        "EC2-75-101-238-215.COMPUTE-1.AMAZONAWS.COM:30005",
        "IP-10-165-19-246:30005"
      ],
      "finalHostType": null
    }
  ],
  "localTime": {"$date": ${localTime?c}},
  "compressedCmdLineOpts": {
    "parsed": {
      "port": 30000,
      "configdb": "ec2-75-101-238-215.compute-1.amazonaws.com:30005",
      "config": "mongodb-cluster-mongos.conf",
      "logpath": "/data/mongos/mongodb.log"
    },
    "argv": [
      "mongos",
      "--config",
      "mongodb-cluster-mongos.conf"
    ],
    "ok": 1
  },
  "normalizedHostnamesAndPorts": [
    "EC2-75-101-238-215.COMPUTE-1.AMAZONAWS.COM:30000",
    "IP-10-165-19-246:30000",
    "*************:30000"
  ],
  "finalHostType": null
}
