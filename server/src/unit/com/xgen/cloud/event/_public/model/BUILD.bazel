load("//server/src/unit:rules.bzl", "unit_package")

unit_package(
    name = "TestLibrary",
    srcs = glob(["*UnitTests.java"]),
    deps = [
        "//server/src/main/com/xgen/cloud/common/auditInfo",
        "//server/src/main/com/xgen/cloud/common/event",
        "//server/src/main/com/xgen/cloud/common/mapstruct/grpc",
        "//server/src/main/com/xgen/cloud/event",
        "//server/src/main/com/xgen/cloud/services/core/util",
        "//server/src/main/com/xgen/cloud/services/event",
        "//server/src/unit/com/xgen/cloud/common/event/_public/sample",
        "//systems/events:event_schemas_java_proto",  # keep
        "//systems/events:java_api_grpc_v0",
        "//systems/prototypes:comment_grpc_v0",
        "@maven//:com_google_protobuf_protobuf_java",
        "@maven//:org_junit_jupiter_junit_jupiter_api",
        "@maven//:org_mockito_mockito_junit_jupiter",
    ],
)
