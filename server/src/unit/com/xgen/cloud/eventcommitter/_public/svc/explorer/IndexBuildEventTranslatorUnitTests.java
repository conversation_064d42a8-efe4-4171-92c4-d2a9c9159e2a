package com.xgen.cloud.eventcommitter._public.svc.explorer;

import com.google.protobuf.InvalidProtocolBufferException;
import com.xgen.cloud.activity._public.model.event.Event;
import com.xgen.cloud.activity._public.model.event.EventType;
import com.xgen.cloud.eventcommitter._public.svc.BaseTranslatorUnitTest;
import com.xgen.cloud.explorer.activity._public.event.IndexBuildEvent;
import com.xgen.cloud.services.event.proto.EventMessage;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.mapstruct.factory.Mappers;

public class IndexBuildEventTranslatorUnitTests extends BaseTranslatorUnitTest {

  @ParameterizedTest
  @EnumSource(IndexBuildEvent.Type.class)
  public void testTranslator(final EventType eventType) throws InvalidProtocolBufferException {
    IndexBuildEvent.Builder builder = IndexBuildEvent.newBuilder();
    builder.eventType(eventType);
    addDefaultFieldsToBuilder(builder);
    IndexBuildEvent event = builder.build();

    IndexBuildEventTranslator translator = Mappers.getMapper(IndexBuildEventTranslator.class);
    final EventMessage eventMessage = translator.translateEventToProtoMessage(event);
    final Event translatedEvent = translator.translateEventFromProtoMessage(eventMessage);
    assertAllFieldsEqual(event, translatedEvent);
  }
}
