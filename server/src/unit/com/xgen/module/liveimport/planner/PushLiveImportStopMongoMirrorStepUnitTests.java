package com.xgen.module.liveimport.planner;

import static com.xgen.module.liveimport.model.RemoteAction.START_MONGO_MIRROR;
import static com.xgen.module.liveimport.model.RemoteAction.STOP_MONGO_MIRROR;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.xgen.module.common.planner.model.Result;
import com.xgen.module.common.planner.model.Step;
import com.xgen.module.liveimport.dao.PushLiveImportDao;
import com.xgen.module.liveimport.model.MongomirrorPushLiveImport;
import com.xgen.module.liveimport.model.RemoteMigrationProcess.RemoteMigrationProcessBuilder;
import java.util.List;
import java.util.Optional;
import nl.altindag.log.LogCaptor;
import org.junit.jupiter.api.Test;
import org.slf4j.LoggerFactory;

public class PushLiveImportStopMongoMirrorStepUnitTests {

  private final LogCaptor logCaptor =
      LogCaptor.forClass(PushLiveImportStopMongoMirrorStepUnitTests.class);
  private PushLiveImportPlanContext _planContext;
  private MongomirrorPushLiveImport _liveImport;

  @Test
  public void testPerformInternal_alreadyDone() {
    final PushLiveImportStopMongoMirrorStep step = getMockedStep();

    doReturn(true).when(step).isPerformed();
    assertTrue(step.performInternal().getStatus().isDone());
    verify(step).isPerformed();
    assertEquals(2, logCaptor.getLogs().size());
  }

  @Test
  public void testPerformInternal_isAcknowledged() {
    final PushLiveImportStopMongoMirrorStep step = getMockedStep();
    doReturn(false).when(step).isPerformed();
    when(_liveImport.getRemoteActionName()).thenReturn(STOP_MONGO_MIRROR);
    when(_liveImport.isCurrentActionAcknowledged()).thenReturn(true);

    assertTrue(step.performInternal().getStatus().isDone());

    assertEquals(2, logCaptor.getLogs().size());
    verify(_liveImport).getRemoteActionName();
    verify(_liveImport).isCurrentActionAcknowledged();
  }

  @Test
  public void testPerformInternal() {
    final PushLiveImportStopMongoMirrorStep step = getMockedStep();
    doReturn(false).when(step).isPerformed();

    final PushLiveImportDao pushLiveImportDao = mock(PushLiveImportDao.class);
    when(_liveImport.getRemoteActionName()).thenReturn(START_MONGO_MIRROR);
    when(_liveImport.isCurrentActionAcknowledged()).thenReturn(false);
    when(_liveImport.getRemoteActionMongomirrors())
        .thenReturn(
            List.of(
                RemoteMigrationProcessBuilder.aRemoteMigrationProcess()
                    .withShardIndex(0)
                    .withVersion("0.12.3")
                    .withMigrationHost("hostname")
                    .build()));
    when(_planContext.getLiveImportDao()).thenReturn(pushLiveImportDao);
    doReturn(Result.inProgress())
        .when(step)
        .evaluateAttempt(anyBoolean(), anyString(), any(), anyString());

    when(pushLiveImportDao.findPushLiveImport(any(), any())).thenReturn(Optional.of(_liveImport));
    final Result<Result.NoData> result = step.performInternal();
    assertFalse(result.getStatus().isDone());
    assertEquals(result, Result.inProgress());
    verify(_planContext).getLiveImportDao();
    verify(_liveImport, times(3)).getRemoteActionName();
    verify(pushLiveImportDao).updateStateStepName(any(), any(), eq(STOP_MONGO_MIRROR));
  }

  private PushLiveImportStopMongoMirrorStep getMockedStep() {
    _planContext = mock(PushLiveImportPlanContext.class);
    _liveImport = mock(MongomirrorPushLiveImport.class);

    when(_planContext.getLogger())
        .thenReturn(LoggerFactory.getLogger(PushLiveImportStopMongoMirrorStepUnitTests.class));
    when(_planContext.getLiveImportDao()).thenReturn(mock(PushLiveImportDao.class));

    return spy(
        new PushLiveImportStopMongoMirrorStep(_planContext, mock(Step.State.class), _liveImport));
  }
}
