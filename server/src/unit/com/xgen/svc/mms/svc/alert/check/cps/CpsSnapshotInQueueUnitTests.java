package com.xgen.svc.mms.svc.alert.check.cps;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doCallRealMethod;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.spy;

import com.google.common.collect.Lists;
import com.xgen.cloud.activity._public.model.alert.state.AbstractThreshold.Operator;
import com.xgen.cloud.activity._public.model.alert.state.IntegerThreshold;
import com.xgen.cloud.alerts.checks.common._public.svc.Result;
import com.xgen.cloud.alerts.checks.common._public.svc.Result.WithoutState;
import com.xgen.cloud.alerts.checks.cps._public.svc.CpsBackupAlertCheckTarget;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.model._public.math.Units;
import com.xgen.cloud.common.util._public.time.TimeUtils2;
import com.xgen.cloud.cps.backupjob._public.model.BackupJob;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.monitoring.topology._public.model.HostType;
import com.xgen.cloud.monitoring.topology._public.model.MonitoredHost;
import com.xgen.cloud.monitoring.topology._public.model.ReplicaSet.Member.State;
import com.xgen.cloud.nds.activity._public.alert.config.CpsBackupAlertConfigWithThreshold;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.svc.mms.BaseUTest;
import com.xgen.svc.mms.svc.alert.GroupAlertProcessingContext;
import com.xgen.svc.nds.util.EncryptionAtRestUtil;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import org.apache.commons.lang3.time.DateUtils;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class CpsSnapshotInQueueUnitTests extends BaseUTest {

  private static final ObjectId PROJECT_ID = ObjectId.get();
  private static final ObjectId CLUSTER_ID = ObjectId.get();
  private static final String CLUSTER_NAME = "Cluster1";
  private static final Date SNAPSHOT_SCHEDULED_CREATION_DATE = new Date();
  private static final Group PROJECT = new Group();

  private static final BackupJob BACKUP_JOB =
      BackupJob.builder()
          .projectId(PROJECT_ID)
          .clusterUniqueId(CLUSTER_ID)
          .clusterName(CLUSTER_NAME)
          .nextSnapshotDate(SNAPSHOT_SCHEDULED_CREATION_DATE)
          .build();

  private static final CpsBackupAlertCheckTarget TARGET = new CpsBackupAlertCheckTarget(BACKUP_JOB);

  @Mock private GroupAlertProcessingContext context;
  @Mock private AppSettings appSettings;
  @Mock private CpsBackupAlertConfigWithThreshold config;

  private Date now;
  private Date threeMinutesAgo;
  private Date sixMinutesAgo;
  private Date eightDaysAgo;
  private CpsSnapshotInQueue underTest;
  private CpsBackupAlertCheckTarget target;
  private ClusterDescription mockClusterDescription;

  @BeforeEach
  public void setup() {
    now = new Date();
    doReturn(now.toInstant()).when(context).getCurrentInstant();
    doReturn(PROJECT_ID).when(context).getGroupId();

    sixMinutesAgo = DateUtils.addMinutes(now, -6);
    threeMinutesAgo = DateUtils.addMinutes(now, -3);
    eightDaysAgo = DateUtils.addDays(now, -8);

    IntegerThreshold fiveMinutes = new IntegerThreshold(Operator.GREATER_THAN, 5, Units.MINUTES);
    doReturn(fiveMinutes).when(config).getThreshold();

    underTest = spy(new CpsSnapshotInQueue(appSettings));

    PROJECT.setId(PROJECT_ID);

    target = spy(TARGET);
    doReturn(true).when(target).isClusterHealthy();

    doReturn(PROJECT_ID).when(context).getGroupId();
    doReturn(CLUSTER_NAME).when(target).getClusterName();

    mockClusterDescription = mock(ClusterDescription.class);
    doReturn(Optional.of(mockClusterDescription)).when(context).getClusterDescription(any(), any());
  }

  @Test
  public void testDoRunCheck_noQueuedSnapshots() {
    List<BackupSnapshot> snapshots = new ArrayList<>();
    doReturn(snapshots).when(context).getQueuedCpsSnapshots(CLUSTER_ID);

    Result.WithoutState result = underTest.doRunCheck(context, config, target);
    assertEquals(WithoutState.NO_ALERT, result);
  }

  @Test
  public void testDoRunCheck_queuedSnapshotOverThreshold() {
    doReturn(Lists.newArrayList(sixMinutesAgo))
        .when(context)
        .getQueuedCpsSnapshotScheduledCreationDates(CLUSTER_ID);

    try (MockedStatic<EncryptionAtRestUtil> mockedStatic = mockStatic(EncryptionAtRestUtil.class)) {
      mockedStatic
          .when(() -> EncryptionAtRestUtil.isEncryptionAtRestEnabledAndInvalid(any(), any()))
          .thenReturn(false);

      // when cluster is healthy
      Result.WithoutState result = underTest.doRunCheck(context, config, target);
      assertEquals(WithoutState.HAS_ALERT, result);

      // when cluster is not healthy but it has not passed 7 days since threshold
      doReturn(false).when(target).isClusterHealthy();
      assertEquals(WithoutState.NO_ALERT, underTest.doRunCheck(context, config, target));

      // when cluster is not healthy but it has passed 7 days since threshold
      doReturn(Lists.newArrayList(eightDaysAgo))
          .when(context)
          .getQueuedCpsSnapshotScheduledCreationDates(CLUSTER_ID);
      assertEquals(WithoutState.HAS_ALERT, underTest.doRunCheck(context, config, target));
    }
  }

  @Test
  public void testDoRunCheck_queuedSnapshotUnderThreshold() {
    BackupSnapshot snapshot = mock(BackupSnapshot.class);
    doReturn(threeMinutesAgo).when(snapshot).getScheduledCreationDate();

    doReturn(Lists.newArrayList(snapshot)).when(context).getQueuedCpsSnapshots(CLUSTER_ID);

    Result.WithoutState result = underTest.doRunCheck(context, config, target);
    assertEquals(WithoutState.NO_ALERT, result);
  }

  @Test
  public void testDoRunCheck_oneSnapshotOverThreshold() {
    doReturn(Lists.newArrayList(threeMinutesAgo, sixMinutesAgo))
        .when(context)
        .getQueuedCpsSnapshotScheduledCreationDates(CLUSTER_ID);

    Result.WithoutState result = underTest.doRunCheck(context, config, target);
    assertEquals(WithoutState.HAS_ALERT, result);
  }

  @Test
  public void testIsClusterHealthy() {
    doCallRealMethod().when(underTest).isClusterHealthy(any(), any());

    try (MockedStatic<EncryptionAtRestUtil> mockedStatic = mockStatic(EncryptionAtRestUtil.class)) {
      mockedStatic
          .when(() -> EncryptionAtRestUtil.isEncryptionAtRestEnabledAndInvalid(any(), any()))
          .thenReturn(false);

      // when clusterDescription is empty
      doReturn(Optional.empty()).when(context).getClusterDescription(any(), any());
      assertFalse(underTest.isClusterHealthy(context, CLUSTER_NAME));

      final ClusterDescription clusterDescription = mock(ClusterDescription.class);
      doReturn(Optional.of(clusterDescription))
          .when(context)
          .getClusterDescription(PROJECT_ID, CLUSTER_NAME);

      // when the host types are valid and last pings are within threshold
      doReturn(
              List.of(
                  getMockedMonitoredHost(0, State.PRIMARY, HostType.REPLICA_PRIMARY),
                  getMockedMonitoredHost(1, State.PRIMARY, HostType.REPLICA_SECONDARY),
                  getMockedMonitoredHost(2, State.PRIMARY, HostType.SHARD_CONFIG_PRIMARY),
                  getMockedMonitoredHost(3, State.PRIMARY, HostType.SHARD_CONFIG_SECONDARY),
                  getMockedMonitoredHost(4, State.PRIMARY, HostType.SHARD_MONGOS)))
          .when(underTest)
          .getMonitoredHosts(context, clusterDescription);

      assertTrue(underTest.isClusterHealthy(context, CLUSTER_NAME));

      // when the host types are valid and last pings are within threshold except one host has null
      // last ping
      doReturn(
              List.of(
                  getMockedMonitoredHost(0, State.PRIMARY, HostType.REPLICA_PRIMARY),
                  getMockedMonitoredHost(1, State.PRIMARY, HostType.REPLICA_SECONDARY),
                  getMockedMonitoredHost(2, State.PRIMARY, HostType.SHARD_CONFIG_PRIMARY),
                  getMockedMonitoredHost(3, State.PRIMARY, HostType.SHARD_CONFIG_SECONDARY),
                  getMockedMonitoredHost(null, State.PRIMARY, HostType.SHARD_MONGOS)))
          .when(underTest)
          .getMonitoredHosts(context, clusterDescription);

      assertFalse(underTest.isClusterHealthy(context, CLUSTER_NAME));

      // when the host types are valid and last pings exceed threshold
      doReturn(List.of(getMockedMonitoredHost(6, State.PRIMARY, HostType.REPLICA_PRIMARY)))
          .when(underTest)
          .getMonitoredHosts(context, clusterDescription);

      assertFalse(underTest.isClusterHealthy(context, CLUSTER_NAME));

      // when the host types are invalid but last pings are within threshold
      doReturn(List.of(getMockedMonitoredHost(3, State.PRIMARY, HostType.RECOVERING)))
          .when(underTest)
          .getMonitoredHosts(context, clusterDescription);

      // when the host states are invalid but last pings are within threshold
      doReturn(List.of(getMockedMonitoredHost(3, State.ROLLBACK, HostType.REPLICA_PRIMARY)))
          .when(underTest)
          .getMonitoredHosts(context, clusterDescription);

      assertFalse(underTest.isClusterHealthy(context, CLUSTER_NAME));

      // when the monitor host results are null
      doReturn(null).when(underTest).getMonitoredHosts(context, clusterDescription);

      assertFalse(underTest.isClusterHealthy(context, CLUSTER_NAME));

      // when the monitor host results are empty
      doReturn(List.of()).when(underTest).getMonitoredHosts(context, clusterDescription);
      assertFalse(underTest.isClusterHealthy(context, CLUSTER_NAME));
    }
  }

  @Test
  public void doRunCheckEncryptionAtRestEnabledAndInvalid() {
    // Cluster is unhealthy - encryption is enabled and invalid and a snapshot has been queued
    doReturn(Lists.newArrayList(eightDaysAgo))
        .when(context)
        .getQueuedCpsSnapshotScheduledCreationDates(CLUSTER_ID);
    doReturn(false).when(target).isClusterHealthy();

    try (MockedStatic<EncryptionAtRestUtil> mockedStatic = mockStatic(EncryptionAtRestUtil.class)) {
      mockedStatic
          .when(() -> EncryptionAtRestUtil.isEncryptionAtRestEnabledAndInvalid(any(), any()))
          .thenReturn(true);

      assertEquals(WithoutState.NO_ALERT, underTest.doRunCheck(context, config, target));
    }
  }

  private MonitoredHost getMockedMonitoredHost(
      final Integer lastPingMinutesAgo, final State replicaState, final HostType... hostTypes) {
    final MonitoredHost host = mock(MonitoredHost.class);

    doReturn(List.of(hostTypes)).when(host).getTypes();
    doReturn(Arrays.stream(hostTypes).map(t -> t.getCode()).collect(Collectors.toList()))
        .when(host)
        .getTypeIds();
    doReturn(replicaState).when(host).getReplicaState();

    if (lastPingMinutesAgo == null) {
      doReturn(null).when(host).getLastPing();
    } else {
      final Calendar lastPingTime = TimeUtils2.nowTime();
      TimeUtils2.addMinutes(lastPingTime, -lastPingMinutesAgo);
      doReturn(lastPingTime.getTime()).when(host).getLastPing();
    }

    return host;
  }
}
