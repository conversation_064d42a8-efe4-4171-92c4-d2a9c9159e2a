package com.xgen.module.integrations.kubernetes.svc;

import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.apiuser._public.svc.ApiUserSvc;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.ApiUser;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.cloud.user._public.svc.UserAllowListSvc;
import com.xgen.module.integrations.kubernetes.view.ApiKeysView;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import org.apache.commons.lang3.StringUtils;

@Singleton
public class KubernetesApiUserSvc {
  private final ApiUserSvc apiUserSvc;
  private final UserAllowListSvc userAllowListSvc;
  private final AppSettings _appSettings;

  @Inject
  public KubernetesApiUserSvc(
      final AppSettings pAppSettings,
      final ApiUserSvc pApiUserSvc,
      final UserAllowListSvc pUserAllowListSvc) {
    _appSettings = pAppSettings;
    apiUserSvc = pApiUserSvc;
    userAllowListSvc = pUserAllowListSvc;
  }

  public boolean kubernetesEnabled(final Organization pOrg) {
    return (_appSettings.getBoolProp("mms.kubernetes.enabled", false) && !pOrg.isAtlas());
  }

  public ApiKeysView generateKeys(
      final List<String> pAccessList,
      final AppUser pUser,
      final Organization pOrg,
      final AuditInfo pAuditInfo)
      throws SvcException {
    Objects.requireNonNull(pAccessList);
    for (final String ip : pAccessList) {
      userAllowListSvc.validate(ip, pUser.getType());
    }

    final String randomUUID = UUID.randomUUID().toString();
    final String apiDescription =
        "K8S Operator - " + StringUtils.substring(randomUUID, 0, Math.min(randomUUID.length(), 8));

    ApiUser user = null;
    try {
      final String username = apiUserSvc.generateApiUserUsername();
      user =
          apiUserSvc.createApiUser(
              username, apiDescription, pOrg, null, List.of(Role.ORG_OWNER), pAuditInfo);
      for (final String ip : pAccessList) {
        userAllowListSvc.add(user.getUserId(), pUser.getType(), ip);
      }
      return new ApiKeysView(username, user.getPrivateKey());
    } catch (final SvcException e) {
      if (user != null) {
        try {
          apiUserSvc.deleteApiUser(user, pAuditInfo);
        } catch (final SvcException apiError) {
          e.addSuppressed(apiError);
        }
      }
      throw e;
    }
  }
}
