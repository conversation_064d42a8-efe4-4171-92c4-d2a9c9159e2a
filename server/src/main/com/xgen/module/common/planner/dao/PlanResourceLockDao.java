package com.xgen.module.common.planner.dao;

import static com.mongodb.client.model.Filters.eq;

import com.mongodb.BasicDBObject;
import com.mongodb.DuplicateKeyException;
import com.mongodb.MongoWriteException;
import com.xgen.cloud.common.db.legacy._public.svc.MongoSvc;
import com.xgen.module.common.planner.model.PlanResourceLock;
import com.xgen.svc.core.dao.base.BaseTDao;
import org.bson.conversions.Bson;

public abstract class PlanResourceLockDao<T extends PlanResourceLock> extends BaseTDao<T> {

  public PlanResourceLockDao(final MongoSvc pMongoSvc, final String pDB, final String pCollection) {
    super(pMongoSvc, pDB, pCollection);
  }

  public PlanResourceLockDao(
      final MongoSvc pMongoSvc,
      final String pConnectionName,
      final String pDB,
      final String pCollection) {
    super(pMongoSvc, pConnectionName, pDB, pCollection);
  }

  public boolean reserveLockRights(final T pPlanResourceLock) {
    try {
      insertMajority(pPlanResourceLock);
      return true;
    } catch (final MongoWriteException | DuplicateKeyException e) {
      // Don't allow multiple moves to place an order for the same domain at the same time.
      return false;
    }
  }

  public boolean releaseLockRights(final T pPlanResourceLock) {
    // Challenges processing rights can only be released by the move that has reserved them.
    final Bson query =
        and(
            eq(PlanResourceLock.FieldDefs.ID, pPlanResourceLock.getId()),
            eq(PlanResourceLock.FieldDefs.PROCESSOR_ID, pPlanResourceLock.getProcessorId()));
    return deleteOneMajority(query).wasAcknowledged();
  }

  public boolean replaceLockRights(final T pReservedLock, final T pPendingLock) {
    final Bson query =
        new BasicDBObject()
            .append(PlanResourceLock.FieldDefs.ID, pReservedLock.getId())
            .append(PlanResourceLock.FieldDefs.PROCESSOR_ID, pReservedLock.getProcessorId());
    final Bson update =
        new BasicDBObject()
            .append(
                UpdateOperators.SET,
                new BasicDBObject()
                    .append(PlanResourceLock.FieldDefs.PROCESSOR_ID, pPendingLock.getProcessorId())
                    .append(PlanResourceLock.FieldDefs.PLAN_ID, pPendingLock.getPlanId()));

    return updateOneMajority(query, update).getModifiedCount() != 0;
  }
}
