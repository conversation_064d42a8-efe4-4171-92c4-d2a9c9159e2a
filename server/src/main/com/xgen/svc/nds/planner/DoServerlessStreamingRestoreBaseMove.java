package com.xgen.svc.nds.planner;

import static com.xgen.svc.nds.planner.ExecuteAutomationAgentJobStep.StateFields.AGENT_JOB_ID;

import com.xgen.cloud.atm.core._public.svc.AutomationAgentAuditSvc;
import com.xgen.cloud.atm.publish._public.svc.AutomationConfigPublishingSvc;
import com.xgen.cloud.common.jobqueue._public.model.AgentJob;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.cps.restore._public.model.ReplicaSetBackupRestoreJob;
import com.xgen.cloud.cps.restore._public.model.ServerlessRestoreMetadata;
import com.xgen.cloud.cps.restore._public.model.ServerlessStreamingBackupRestoreJob;
import com.xgen.cloud.deployment._public.model.MongoDbVersion;
import com.xgen.cloud.monitoring.lifecycle._public.svc.HostClusterLifecycleSvc;
import com.xgen.cloud.monitoring.topology._public.model.HostCluster;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.NDSDefaults;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.serverless._public.model.ServerlessMTMCluster;
import com.xgen.cloud.nds.serverlessbackup._public.jobs.ServerlessStreamingRestoreJob;
import com.xgen.module.common.planner.PlanAbandonedException;
import com.xgen.module.common.planner.model.PlanContext;
import com.xgen.module.common.planner.model.Result;
import com.xgen.module.common.planner.model.Result.NoData;
import com.xgen.module.common.planner.model.Step;
import com.xgen.svc.nds.serverless.planner.backup.WaitForServerlessRestoreLockStep;
import com.xgen.svc.nds.svc.ServerlessBackupSvc;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.types.ObjectId;

public abstract class DoServerlessStreamingRestoreBaseMove<
        T extends ServerlessStreamingBackupRestoreJob>
    extends DoServerlessSnapshotRestoreBaseMove {
  private static final int AGENT_PHASE1_RETRY_COUNT = 0;
  private static final int AGENT_PHASE2_RETRY_COUNT = 0;
  private static final int AGENT_CLEANUP_RETRY_COUNT = 3;

  private static final Duration AGENT_PHASE1_TIMEOUT = Duration.ofDays(5);
  private static final Duration AGENT_PHASE2_TIMEOUT = Duration.ofDays(1);
  private static final Duration AGENT_CLEANUP_TIMEOUT = Duration.ofMinutes(30);

  private final AtomicReference<T> _restoreJob = new AtomicReference<>();
  private final AtomicReference<ServerlessMTMCluster> _targetMtmCluster = new AtomicReference<>();
  private final AtomicReference<String> _targetMtmRsId = new AtomicReference<>();
  private final AtomicReference<HostCluster> _targetMTMHostCluster = new AtomicReference<>();
  private final AtomicReference<ObjectId> _targetMtmClusterId = new AtomicReference<>();

  private final ClusterDescriptionDao _clusterDescriptionDao;
  private final HostClusterLifecycleSvc _hostClusterLifecycleSvc;

  public DoServerlessStreamingRestoreBaseMove(
      final PlanContext pContext,
      final ObjectId pRestoreJobId,
      final BackupDependenciesProvider pBackupDependenciesProvider,
      final ServerlessBackupSvc pServerlessBackupSvc,
      final HostClusterLifecycleSvc pHostClusterLifecycleSvc,
      final ClusterDescriptionDao pClusterDescriptionDao,
      final AutomationConfigPublishingSvc pAutomationConfigSvc,
      final AutomationAgentAuditSvc pAutomationAgentAuditSvc) {
    super(
        pContext,
        pRestoreJobId,
        pBackupDependenciesProvider,
        pServerlessBackupSvc,
        pAutomationConfigSvc,
        pAutomationAgentAuditSvc);
    _clusterDescriptionDao = pClusterDescriptionDao;
    _hostClusterLifecycleSvc = pHostClusterLifecycleSvc;
  }

  public DoServerlessStreamingRestoreBaseMove(
      final ObjectId pId,
      final Set<ObjectId> pPredecessors,
      final Set<ObjectId> pSuccessors,
      final PlanContext pContext,
      final State pState,
      final ObjectId pRestoreJobId,
      final BackupDependenciesProvider pBackupDependenciesProvider,
      final ServerlessBackupSvc pServerlessBackupSvc,
      final HostClusterLifecycleSvc pHostClusterLifecycleSvc,
      final ClusterDescriptionDao pClusterDescriptionDao,
      final AutomationConfigPublishingSvc pAutomationConfigSvc,
      final AutomationAgentAuditSvc pAutomationAgentAuditSvc) {
    super(
        pId,
        pPredecessors,
        pSuccessors,
        pContext,
        pState,
        pRestoreJobId,
        pBackupDependenciesProvider,
        pServerlessBackupSvc,
        pAutomationConfigSvc,
        pAutomationAgentAuditSvc);
    _clusterDescriptionDao = pClusterDescriptionDao;
    _hostClusterLifecycleSvc = pHostClusterLifecycleSvc;
  }

  public AtomicReference<T> getRestoreJob() {
    return _restoreJob;
  }

  @Override
  public Result<NoData> rollbackInternal() {
    return getTargetMtmHostList().map(this::performRestoreCleanup).orElse(Result.done());
  }

  protected Result<NoData> performRestoreCleanup(final List<String> pTargetHostNames) {
    final List<Result<?>> results =
        IntStream.range(0, pTargetHostNames.size())
            .mapToObj(i -> performRestoreCleanup(pTargetHostNames.get(i), i))
            .collect(Collectors.toList());

    return runIfReady(() -> Result.done(), results);
  }

  protected Result<?> performRestoreCleanup(final String pTargetHostName, final int pIndex) {
    final ExecuteAutomationAgentJobStep phase1JobStep = getAgentJobPhase1(pTargetHostName, pIndex);

    if (!phase1JobStep.hasPerformStarted()) {
      // Nothing to cleanup, if phase1 is not started
      return Result.done();
    }
    final ObjectId targetMTMClusterUniqueId =
        getBackupRestoreJob().getServerlessRestoreMetadata().getTargetMTMClusterUniqueId();
    final ObjectId targetMTMClusterGroupId =
        getBackupRestoreJob().getServerlessRestoreMetadata().getTargetMTMClusterGroupId();
    try {
      if (isTargetMTMDeletedOrDeleteRequested(targetMTMClusterGroupId, targetMTMClusterUniqueId)) {
        getLogger()
            .atInfo()
            .setMessage(
                String.format(
                    "Skipping executing the clean up job on target MTM %s as it has deleted or"
                        + " has been requested to delete",
                    targetMTMClusterUniqueId))
            .log();
        return Result.done();
      }
    } catch (Exception pE) {
      getLogger()
          .atError()
          .setMessage(
              String.format(
                  "Failed to decide if the target MTM %s gets terminated",
                  targetMTMClusterUniqueId))
          .setCause(pE)
          .log();
      return Result.done();
    }
    if (phase1JobStep.isPerformed()) {
      final ExecuteAutomationAgentJobStep phase2JobStep =
          getAgentJobPhase2(pTargetHostName, pIndex);
      if (phase2JobStep.hasPerformStarted()
          && !phase2JobStep.isPerformed()
          && phase2JobStep.perform().getStatus().isInProgress()) {
        // wait for phase2 before sending a cleanup job
        return Result.inProgress();
      }
    } else if (phase1JobStep.perform().getStatus().isInProgress()) {
      // wait for phase1 before sending a cleanup job
      return Result.inProgress();
    }
    return executeAgentCleanupJob(pTargetHostName, pIndex);
  }

  private boolean isTargetMTMDeletedOrDeleteRequested(ObjectId mtmGroupId, ObjectId mtmClusterId) {
    final ClusterDescription targetMTMClusterDescription =
        _clusterDescriptionDao
            .findByUniqueId(mtmGroupId, mtmClusterId)
            .orElseThrow(() -> new PlanAbandonedException((NDSErrorCode.CLUSTER_NOT_FOUND)));
    return targetMTMClusterDescription.isDeletedOrDeleteRequested();
  }

  private ExecuteAutomationAgentJobStep getCleanupAgentJobStep(
      final String pHostName, final int pIndex, final String targetMTMRsId) {
    ServerlessMTMCluster targetMTMCluster = getTargetServerlessMTMCluster();

    final MongoDbVersion sourceVersion = getSourceMongoDbVersion();
    final MongoDbVersion targetVersion = getTargetMongoDbVersion();
    final List<MongoDbVersion> upgradePathToTargetVersions =
        getServerlessBackupSvc()
            .getUpgradePathToTargetVersion(
                getBackupRestoreJob().getProjectId(),
                getBackupRestoreJob().getClusterName(),
                sourceVersion,
                targetVersion);
    final List<String> upgradePathToTargetFcv =
        getServerlessBackupSvc()
            .getUpgradeFcvPath(
                getServerlessBackupSvc()
                    .getTargetClusterDescription(
                        getBackupRestoreJob().getServerlessRestoreMetadata()),
                upgradePathToTargetVersions);
    final AgentJob agentJob =
        ServerlessStreamingRestoreJob.createCleanupJob(
            targetMTMCluster.getGroupId(),
            getBackupRestoreJob(),
            isGcpToGcpCrossProjRestore(getBackupRestoreJob(), targetMTMCluster),
            pHostName,
            sourceVersion,
            upgradePathToTargetVersions,
            upgradePathToTargetFcv,
            targetMTMRsId,
            getBackupDependenciesProvider()
                .getAppSettings()
                .getServerlessBackupImportCollectionWorkers());
    return getExecuteAutomationAgentJobStep(
        getState()
            .forStep(StepNumber.EXECUTE_SERVERLESS_RESTORE_AGENT_CLEANUP_JOB)
            .toMultiState(pIndex),
        agentJob,
        AGENT_CLEANUP_RETRY_COUNT,
        AGENT_CLEANUP_TIMEOUT);
  }

  private Result<?> executeAgentCleanupJob(final String pHostName, final int pIndex) {
    return performTargetMTMAgentJobIfHostUp(
        getCleanupAgentJobStep(pHostName, pIndex, getTargetMtmRsId()), pHostName, false);
  }

  @Override
  public Result<NoData> performInternal() {
    final Pair<Boolean, ReplicaSetBackupRestoreJob> backupRestoreJobPair =
        validateBackupRestoreJob();

    if (backupRestoreJobPair.getLeft()) {
      return Result.done();
    }

    final ServerlessStreamingBackupRestoreJob backupRestoreJob =
        (ServerlessStreamingBackupRestoreJob) backupRestoreJobPair.getRight();

    final Result<?> restoreReadyResult = runIfReady(() -> waitForRestoreReady());

    List<String> targetHostNames = getOrCreateTargetMTMHostList();

    setTargetMtmRsId();

    final Result<?> restorePhase1Result =
        runIfReady(() -> performRestorePhase1(targetHostNames), restoreReadyResult);

    Result<NoData> waitForRestoreJobLock =
        runIfReady(
            () -> waitForServerlessRestoreLock(getTargetMtmClusterId()), restorePhase1Result);

    final Result<?> restorePhase2Result =
        runIfReady(
            () -> performRestorePhase2(targetHostNames),
            waitForRestoreJobLock,
            restorePhase1Result);

    final Result<?> cleanRestoreJobResult =
        runIfReady(
            () -> {
              performCleanupRestoreJobId(backupRestoreJob);
              return Result.done();
            },
            restorePhase1Result,
            restorePhase2Result);

    return runIfReady(
        () -> {
          getServerlessBackupSvc()
              .releaseServerlessRestoreJobLock(
                  getId(), getNDSPlanContext().getPlanId(), getTargetMtmClusterId());
          return Result.done();
        },
        cleanRestoreJobResult);
  }

  protected Result<?> waitForRestoreReady() {
    final ServerlessStreamingBackupRestoreJob backupRestoreJob = getBackupRestoreJob();
    return backupRestoreJob.isRestoreServerRunning() ? Result.done() : Result.inProgress();
  }

  protected Result<?> performRestorePhase1(final List<String> pTargetHostNames) {
    final List<Result<?>> results =
        IntStream.range(0, pTargetHostNames.size())
            .mapToObj(i -> performRestorePhase1(pTargetHostNames.get(i), i))
            .collect(Collectors.toList());
    return runIfReady(() -> Result.done(), results);
  }

  protected Result<?> performRestorePhase1(final String pTargetHostName, final int pIndex) {
    final ExecuteAutomationAgentJobStep agentJobStep = getAgentJobPhase1(pTargetHostName, pIndex);
    return performTargetMTMAgentJobIfHostUp(agentJobStep, pTargetHostName, true);
  }

  private void resetPhaseOneJobState(List<String> pTargetHostNames) {
    if (pTargetHostNames.isEmpty()) {
      throw new IllegalStateException(
          "can not reset phase one job state since can not find host name for target MTM");
    }
    // Unset these two fields for phase one job so that we can rerun the step again
    // Unfortunately it is a little hacky, as we can only change the state of the step indirectly
    IntStream.range(0, pTargetHostNames.size())
        .forEach(
            i -> {
              ExecuteAutomationAgentJobStep phase1JobStep =
                  getAgentJobPhase1(pTargetHostNames.get(i), i);
              resetStateForAgentJobStep(phase1JobStep);
            });
  }

  private void resetStateForAgentJobStep(
      ExecuteAutomationAgentJobStep pExecuteAutomationAgentJobStep) {
    pExecuteAutomationAgentJobStep.setStateValue("resetStatePerformed", true);
    // https://jira.mongodb.org/browse/CLOUDP-193241 - breaks step encapsulation; ideally reworked
    pExecuteAutomationAgentJobStep.setStateValue(
        Step.StateFields_DoNotIntroduceNewReferences.PERFORM_ENDED_DATE, null);
    pExecuteAutomationAgentJobStep.setStateValue(
        Step.StateFields_DoNotIntroduceNewReferences.PERFORMED, null);
    pExecuteAutomationAgentJobStep.setStateValue(
        Step.StateFields_DoNotIntroduceNewReferences.PERFORM_STARTED_DATE, null);
    pExecuteAutomationAgentJobStep.setStateValue(AGENT_JOB_ID, null);
  }

  private ExecuteAutomationAgentJobStep getAgentJobPhase1(
      final String pHostName, final int pIndex) {
    ServerlessMTMCluster targetMTMCluster = getTargetServerlessMTMCluster();
    String targetMTMRsId = getTargetMtmRsId();

    final MongoDbVersion sourceVersion = getSourceMongoDbVersion();
    final MongoDbVersion targetVersion = getTargetMongoDbVersion();
    final List<MongoDbVersion> upgradePathToTargetVersions =
        getServerlessBackupSvc()
            .getUpgradePathToTargetVersion(
                getBackupRestoreJob().getProjectId(),
                getBackupRestoreJob().getClusterName(),
                sourceVersion,
                targetVersion);
    final List<String> upgradePathToTargetFcv =
        getServerlessBackupSvc()
            .getUpgradeFcvPath(
                getServerlessBackupSvc()
                    .getTargetClusterDescription(
                        getBackupRestoreJob().getServerlessRestoreMetadata()),
                upgradePathToTargetVersions);

    final AgentJob agentJob =
        ServerlessStreamingRestoreJob.createPhase1Job(
            targetMTMCluster.getGroupId(),
            getBackupRestoreJob(),
            isGcpToGcpCrossProjRestore(getBackupRestoreJob(), targetMTMCluster),
            pHostName,
            sourceVersion,
            upgradePathToTargetVersions,
            upgradePathToTargetFcv,
            targetMTMRsId,
            getBackupDependenciesProvider()
                .getAppSettings()
                .getServerlessBackupImportCollectionWorkers());

    return getExecuteAutomationAgentJobStep(
        getState()
            .forStep(StepNumber.EXECUTE_SERVERLESS_RESTORE_AGENT_JOB_PHASE_1)
            .toMultiState(pIndex),
        agentJob,
        AGENT_PHASE1_RETRY_COUNT,
        AGENT_PHASE1_TIMEOUT);
  }

  private Result<NoData> performRestorePhase2(final List<String> pTargetHostNames) {
    final ServerlessStreamingBackupRestoreJob restoreJob = getBackupRestoreJob();
    final ServerlessBackupSvc serverlessBackupSvc = getServerlessBackupSvc();

    final List<Result<?>> results =
        IntStream.range(0, pTargetHostNames.size())
            .mapToObj(i -> performRestorePhase2(pTargetHostNames.get(i), i))
            .collect(Collectors.toList());
    return runIfReady(Result::done, results);
  }

  protected Result<?> performRestorePhase2(final String pTargetHostName, final int pIndex) {
    return performTargetMTMAgentJobIfHostUp(
        getAgentJobPhase2(pTargetHostName, pIndex), pTargetHostName, true);
  }

  private ExecuteAutomationAgentJobStep getAgentJobPhase2(
      final String pHostName, final int pIndex) {
    ServerlessMTMCluster targetMTMCluster = getTargetServerlessMTMCluster();
    String targetMTMRsId = getTargetMtmRsId();

    final MongoDbVersion sourceVersion = getSourceMongoDbVersion();
    final MongoDbVersion targetVersion = getTargetMongoDbVersion();
    final List<MongoDbVersion> upgradePathToTargetVersions =
        getServerlessBackupSvc()
            .getUpgradePathToTargetVersion(
                getBackupRestoreJob().getProjectId(),
                getBackupRestoreJob().getClusterName(),
                sourceVersion,
                targetVersion);
    final List<String> upgradePathToTargetFcv =
        getServerlessBackupSvc()
            .getUpgradeFcvPath(
                getServerlessBackupSvc()
                    .getTargetClusterDescription(
                        getBackupRestoreJob().getServerlessRestoreMetadata()),
                upgradePathToTargetVersions);

    final AgentJob agentJob =
        ServerlessStreamingRestoreJob.createPhase2Job(
            targetMTMCluster.getGroupId(),
            getBackupRestoreJob(),
            isGcpToGcpCrossProjRestore(getBackupRestoreJob(), targetMTMCluster),
            pHostName,
            sourceVersion,
            upgradePathToTargetVersions,
            upgradePathToTargetFcv,
            targetMTMRsId,
            getBackupDependenciesProvider()
                .getAppSettings()
                .getServerlessBackupImportCollectionWorkers());

    return getExecuteAutomationAgentJobStep(
        getState()
            .forStep(StepNumber.EXECUTE_SERVERLESS_RESTORE_AGENT_JOB_PHASE_2)
            .toMultiState(pIndex),
        agentJob,
        AGENT_PHASE2_RETRY_COUNT,
        AGENT_PHASE2_TIMEOUT);
  }

  private void performCleanupRestoreJobId(final ReplicaSetBackupRestoreJob pJob) {
    final ClusterDescription clusterDescription =
        _clusterDescriptionDao
            .findByUniqueId(
                Optional.ofNullable(pJob)
                    .map(ServerlessStreamingBackupRestoreJob.class::cast)
                    .map(ServerlessStreamingBackupRestoreJob::getServerlessRestoreMetadata)
                    .map(ServerlessRestoreMetadata::getTargetClusterGroupId)
                    .orElse(null),
                Optional.ofNullable(pJob)
                    .map(ServerlessStreamingBackupRestoreJob.class::cast)
                    .map(ServerlessStreamingBackupRestoreJob::getServerlessRestoreMetadata)
                    .map(ServerlessRestoreMetadata::getTargetClusterUniqueId)
                    .orElseThrow())
            .orElseThrow();

    _clusterDescriptionDao.save(
        clusterDescription
            .copy()
            .setRestoreJobIds(Collections.emptyList())
            .setRestoreJobType(null)
            .build());
  }

  protected Optional<List<String>> getTargetMtmHostList() {
    return getState()
        .getValue(StateFields.SERVERLESS_TARGET_MTM_HOST_NAMES)
        .map(List.class::cast)
        .map(
            list -> {
              final List<String> typedList = new ArrayList<>();
              for (final Object o : list) {
                typedList.add(String.class.cast(o));
              }
              return typedList;
            });
  }

  private void setTargetMtmRsId() {
    getState()
        .getValue(StateFields.SERVERLESS_TARGET_MTM_RS_ID)
        .orElseGet(
            () -> {
              String rsId = getTargetMtmRsId();
              getState().setValue(StateFields.SERVERLESS_TARGET_MTM_RS_ID, rsId);
              return rsId;
            });
  }

  private ObjectId getTargetMtmClusterId() {
    if (_targetMtmClusterId.get() == null) {
      setTargetMtmMetaData();
    }
    return _targetMtmClusterId.get();
  }

  private List<String> getOrCreateTargetMTMHostList() {
    return getTargetMtmHostList().orElseGet(this::createAndSaveTargetMTMHostNames);
  }

  private List<String> createAndSaveTargetMTMHostNames() {
    ServerlessMTMCluster targetMTMCluster = getTargetServerlessMTMCluster();
    final List<String> targetHostNames =
        getBackupDependenciesProvider()
            .getReplicaSetHardwareDao()
            .findByCluster(targetMTMCluster.getGroupId(), targetMTMCluster.getName())
            .stream()
            .flatMap(ReplicaSetHardware::getAllHardware)
            .filter(InstanceHardware::isProvisioned)
            .map(InstanceHardware::getHostnameForAgents)
            .filter(Optional::isPresent)
            .map(Optional::get)
            .collect(Collectors.toList());

    getState().setValue(StateFields.SERVERLESS_TARGET_MTM_HOST_NAMES, targetHostNames);

    return targetHostNames;
  }

  private ServerlessMTMCluster getTargetServerlessMTMCluster() {
    if (_targetMtmCluster.get() == null) {
      final ClusterDescription targetClusterDescription;
      try {
        targetClusterDescription =
            getServerlessBackupSvc()
                .getServerlessClusterDescription(
                    getBackupRestoreJob().getTarget().getTargetProjectId(),
                    getBackupRestoreJob().getTarget().getTargetClusterName());
      } catch (final SvcException e) {
        throw new IllegalArgumentException(e);
      }

      final ServerlessMTMCluster mtmCluster =
          getServerlessBackupSvc()
              .getMtmClusterByTenantId(
                  targetClusterDescription.getGroupId(), targetClusterDescription.getUniqueId());
      _targetMtmCluster.set(mtmCluster);
    }

    return _targetMtmCluster.get();
  }

  private void setTargetMtmMetaData() {
    final ServerlessMTMCluster targetMtmCluster = getTargetServerlessMTMCluster();
    Optional<ClusterDescription> clusterDescription =
        _clusterDescriptionDao.findByName(
            targetMtmCluster.getGroupId(), targetMtmCluster.getName());
    if (clusterDescription.isEmpty() || clusterDescription.get().isServerlessTenantCluster()) {
      throw new PlanAbandonedException(NDSErrorCode.NOT_SERVERLESS_MTM_CLUSTER);
    }
    String targetMtmRsId =
        NDSDefaults.getReplicaSetNameForUnshardedCluster(clusterDescription.get());

    _targetMtmRsId.set(targetMtmRsId);
    ObjectId targetMtmClusterId = clusterDescription.get().getUniqueId();
    _targetMtmClusterId.set(targetMtmClusterId);
  }

  private String getTargetMtmRsId() {
    if (_targetMtmRsId.get() == null) {
      setTargetMtmMetaData();
    }
    return _targetMtmRsId.get();
  }

  protected abstract MongoDbVersion getSourceMongoDbVersion();

  protected abstract MongoDbVersion getTargetMongoDbVersion();

  @Override
  protected abstract ServerlessStreamingBackupRestoreJob getBackupRestoreJob();

  @Override
  protected void performCleanupOnSuccess(final ReplicaSetBackupRestoreJob pBackupRestoreJob) {
    performCleanupRestoreJobId(pBackupRestoreJob);
  }

  @Override
  protected void performCleanupOnCancel(final ReplicaSetBackupRestoreJob pBackupRestoreJob) {
    performCleanupRestoreJobId(pBackupRestoreJob);
  }

  @Override
  protected void performCleanupOnExpire(final ReplicaSetBackupRestoreJob pBackupRestoreJob) {
    performCleanupRestoreJobId(pBackupRestoreJob);
  }

  private Result<?> performTargetMTMAgentJobIfHostUp(
      final ExecuteAutomationAgentJobStep step,
      final String targetMTMHost,
      final boolean failOnTermination) {
    return runIfReady(
        () -> {
          if (!step.hasPerformStarted() && !isTargetMTMHostUp(targetMTMHost)) {
            final ObjectId targetMTMClusterUniqueId =
                getBackupRestoreJob().getServerlessRestoreMetadata().getTargetMTMClusterUniqueId();
            final ObjectId targetMTMClusterGroupId =
                getBackupRestoreJob().getServerlessRestoreMetadata().getTargetMTMClusterGroupId();
            try {
              if (isTargetMTMDeletedOrDeleteRequested(
                  targetMTMClusterGroupId, targetMTMClusterUniqueId)) {
                getLogger()
                    .atInfo()
                    .setMessage(
                        String.format(
                            "Skipping executing of the agent job on target MTM %s as it has been"
                                + " deleted or has been requested to delete",
                            targetMTMClusterUniqueId))
                    .log();
                return failOnTermination ? Result.failed() : Result.done(null);
              }
            } catch (Exception pE) {
              getLogger()
                  .atError()
                  .setMessage(
                      String.format(
                          "Failed to decide if the target MTM %s gets terminated",
                          targetMTMClusterUniqueId))
                  .setCause(pE)
                  .log();
              return Result.failed();
            }
            getLogger()
                .atWarn()
                .setMessage("Host is not up for running agent job. waiting...")
                .addKeyValue("mtmHostName", targetMTMHost)
                .log();

            return Result.inProgress();
          }
          return step.perform();
        });
  }

  private HostCluster getTargetMTMHostCluster() {
    if (_targetMTMHostCluster.get() == null) {
      _targetMTMHostCluster.set(
          _hostClusterLifecycleSvc.getGroupReplicaSetCluster(
              getTargetServerlessMTMCluster().getGroupId(), getTargetMtmRsId(), true));
    }

    return _targetMTMHostCluster.get();
  }

  private boolean isTargetMTMHostUp(final String hostName) {

    HostCluster hostCluster = getTargetMTMHostCluster();

    return hostCluster.getHosts().stream()
        .filter(h -> hostName.equals(h.getName()))
        .map(h -> h.getUpSince() != null)
        .findFirst()
        .orElse(false);
  }

  protected boolean isGcpToGcpCrossProjRestore(
      final ServerlessStreamingBackupRestoreJob pRestoreJob,
      final ServerlessMTMCluster pTargetServerlessMTMCluster) {
    return pRestoreJob.getCloudProvider().equals(CloudProvider.GCP)
        && pTargetServerlessMTMCluster.getProviderName().equals(CloudProvider.GCP)
        && !pRestoreJob
            .getServerlessRestoreMetadata()
            .getSnapshotGroupId()
            .equals(pTargetServerlessMTMCluster.getGroupId());
  }

  protected void setFailureAlertSent() {
    getState().setValue(StateFields.FAILURE_ALERT_SENT, true);
  }

  protected boolean isFailureAlertSent() {
    return getState().getValue(StateFields.FAILURE_ALERT_SENT).isPresent();
  }

  protected static class StateFields {
    protected static final String SERVERLESS_TARGET_MTM_HOST_NAMES = "serverlessTargetMTMHostNames";
    protected static final String SERVERLESS_TARGET_MTM_RS_ID = "serverlessTargetMTMRsId";
    protected static final String FAILURE_ALERT_SENT = "failureAlertSent";
  }

  protected Result<NoData> waitForServerlessRestoreLock(final ObjectId targetClusterId) {
    return new WaitForServerlessRestoreLockStep(
            getContext(),
            getState().forStep(StepNumber.WAIT_FOR_RESTORE_LOCK),
            getId(),
            getNDSPlanContext().getPlanId(),
            targetClusterId,
            getServerlessBackupSvc())
        .perform();
  }

  public static class StepNumber {

    private static final int EXECUTE_SERVERLESS_RESTORE_AGENT_JOB_PHASE_1 = 1;
    private static final int EXECUTE_SERVERLESS_RESTORE_AGENT_JOB_PHASE_2 = 2;
    private static final int EXECUTE_SERVERLESS_RESTORE_AGENT_CLEANUP_JOB = 3;
    // Assign a random number as this will be removed shortly
    private static final int WAIT_FOR_RESTORE_LOCK = 199;
  }
}
