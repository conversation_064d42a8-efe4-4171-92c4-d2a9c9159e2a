package com.xgen.svc.nds.planner.movefactory;

import com.google.inject.assistedinject.Assisted;
import com.xgen.module.common.planner.FromDBConstructor;
import com.xgen.module.common.planner.PlanningConstructor;
import com.xgen.module.common.planner.model.Move;
import com.xgen.module.common.planner.model.PlanContext;
import com.xgen.svc.nds.planner.CollectionRestoreFromSystemClusterMove;
import java.util.Set;
import org.bson.types.ObjectId;

public interface CollectionRestoreFromSystemClusterMoveFactory {

  @FromDBConstructor
  CollectionRestoreFromSystemClusterMove create(
      @Assisted("id") final ObjectId id,
      @Assisted("predecessors") final Set<ObjectId> predecessors,
      @Assisted("successors") final Set<ObjectId> successors,
      @Assisted("planContext") final PlanContext context,
      @Assisted("state") final Move.State state,
      @Assisted("systemClusterJobId") final ObjectId systemClusterJobId);

  @PlanningConstructor
  CollectionRestoreFromSystemClusterMove create(
      @Assisted("planContext") final PlanContext context,
      @Assisted("systemClusterJobId") final ObjectId systemClusterJobId);
}
