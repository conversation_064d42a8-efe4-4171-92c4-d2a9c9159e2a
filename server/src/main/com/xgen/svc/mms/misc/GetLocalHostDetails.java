package com.xgen.svc.mms.misc;

import com.xgen.cloud.brs.core._public.model.HostDetails;

/** Utility to print local HostDetails to standard out in a format that can be sourced by a shell */
public class GetLocalHostDetails {
  public static void main(String[] args) {
    final HostDetails localDetails = HostDetails.getLocalHostDetails();
    System.out.println("architecture=" + localDetails.getArchitecture());
    System.out.println("platform=" + localDetails.getPlatform().name());
    final String flavor = localDetails.getOsFlavor();
    if (!flavor.equals("")) {
      System.out.println("flavor=" + flavor);
    }
    final String version = localDetails.getOsVersion();
    if (!version.equals("")) {
      System.out.println("version=" + version);
    }
  }
}
