package com.xgen.svc.mms.util.billing.scenarios.generic;

import static java.util.Objects.requireNonNullElse;

import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.svc.mms.util.billing.scenarios.generic.model.OrgContainer;
import com.xgen.svc.mms.util.billing.scenarios.generic.model.action.AddSelfServePaymentMethodAction;
import com.xgen.svc.mms.util.billing.scenarios.generic.model.action.ApplyActivationCodeAction;
import com.xgen.svc.mms.util.billing.scenarios.generic.model.action.CreateOrgAction;
import com.xgen.svc.mms.util.billing.scenarios.generic.model.action.LinkOrgsAction;
import com.xgen.svc.mms.util.billing.scenarios.generic.model.action.RunDailyBillingAction;
import com.xgen.svc.mms.util.billing.scenarios.generic.model.action.ScenarioAction;
import com.xgen.svc.mms.util.billing.scenarios.generic.model.request.DailyBillingRunMode;
import com.xgen.svc.mms.util.billing.scenarios.generic.model.request.Scenario;
import com.xgen.svc.mms.util.billing.scenarios.generic.model.request.ScenarioExecutionConfig;
import com.xgen.svc.mms.util.billing.scenarios.generic.model.request.ScenarioInvoice;
import com.xgen.svc.mms.util.billing.scenarios.generic.model.request.ScenarioLinkedOrg;
import com.xgen.svc.mms.util.billing.scenarios.generic.model.request.ScenarioOpportunity;
import com.xgen.svc.mms.util.billing.scenarios.generic.model.request.ScenarioOpportunityCredit;
import com.xgen.svc.mms.util.billing.scenarios.generic.processor.ScenarioProcessorSvc;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.time.Clock;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Singleton
public class GenericBillingScenario {
  private final ScenarioProcessorSvc scenarioProcessorSvc;
  private final Clock clock;

  @Inject
  public GenericBillingScenario(ScenarioProcessorSvc scenarioProcessorSvc, Clock clock) {
    this.scenarioProcessorSvc = scenarioProcessorSvc;
    this.clock = clock;
  }

  public Organization generate(Scenario scenario) {
    validateRequest(scenario);

    // Create a link to the org which will be created later
    OrgContainer payingOrg = new OrgContainer();

    List<ScenarioAction> actions = new ArrayList<>();
    actions.addAll(createPayingOrgActions(payingOrg, scenario));
    actions.addAll(
        createLinkedOrgsActions(payingOrg, scenario.linkedOrgs(), scenario.executionConfig()));
    actions.addAll(createActivationCodeActions(payingOrg, scenario.opportunities()));

    if (scenario.selfServePaymentMethod() != null) {
      LocalDate applyOn =
          requireNonNullElse(
              scenario.selfServePaymentMethod().applyOn(), getOrgCreationDate(scenario.invoices()));
      actions.add(
          new AddSelfServePaymentMethodAction(
              payingOrg, scenario.selfServePaymentMethod(), applyOn));
    }

    actions.stream()
        .sorted(
            Comparator.comparing(ScenarioAction::applyOn).thenComparing(a -> a.type().getOrder()))
        .forEach(scenarioProcessorSvc::run);

    return payingOrg.getOrg();
  }

  private List<ScenarioAction> createPayingOrgActions(
      OrgContainer payingOrgContainer, Scenario scenario) {
    List<ScenarioAction> actions = new ArrayList<>();

    actions.add(
        new CreateOrgAction(
            payingOrgContainer,
            scenario.orgName(),
            getOrgCreationDate(scenario.invoices()),
            scenario.maxOutstandingBillCents(),
            scenario.invoices()));
    actions.addAll(
        createDailyBillingActions(
            payingOrgContainer, scenario.invoices(), scenario.executionConfig()));
    return actions;
  }

  private List<ScenarioAction> createLinkedOrgsActions(
      OrgContainer payingOrgContainer,
      List<ScenarioLinkedOrg> linkedOrgs,
      ScenarioExecutionConfig executionConfig) {
    if (linkedOrgs.isEmpty()) {
      return List.of();
    }
    List<ScenarioAction> actions = new ArrayList<>();
    Map<LocalDate, List<OrgContainer>> linkedOrgContainersByLinkOnDate = new HashMap<>();

    for (ScenarioLinkedOrg linkedOrg : linkedOrgs) {
      // Create a link to the linked org which will be created later
      OrgContainer linkedOrgContainer = new OrgContainer();
      actions.add(
          new CreateOrgAction(
              linkedOrgContainer,
              linkedOrg.orgName(),
              getOrgCreationDate(linkedOrg.invoices()),
              null,
              linkedOrg.invoices()));
      actions.addAll(
          createDailyBillingActions(linkedOrgContainer, linkedOrg.invoices(), executionConfig));

      LocalDate linkOn = Objects.requireNonNullElse(linkedOrg.linkOn(), today());
      linkedOrgContainersByLinkOnDate
          .computeIfAbsent(linkOn, k -> new ArrayList<>())
          .add(linkedOrgContainer);
    }

    linkedOrgContainersByLinkOnDate.forEach(
        (linkOn, linkedOrgContainers) -> {
          actions.add(new LinkOrgsAction(payingOrgContainer, linkedOrgContainers, linkOn));
        });

    return actions;
  }

  private List<ScenarioAction> createDailyBillingActions(
      OrgContainer org, List<ScenarioInvoice> invoices, ScenarioExecutionConfig executionConfig) {
    // Default to MONTHLY behavior if no execution config is provided
    DailyBillingRunMode runMode =
        (executionConfig != null && executionConfig.dailyBillingRunMode() != null)
            ? executionConfig.dailyBillingRunMode()
            : DailyBillingRunMode.MONTHLY;

    if (runMode == DailyBillingRunMode.DAILY) {
      return createDailyBillingActionsDaily(org, invoices);
    } else {
      return createDailyBillingActionsMonthly(org, invoices);
    }
  }

  private List<ScenarioAction> createDailyBillingActionsMonthly(
      OrgContainer org, List<ScenarioInvoice> invoices) {
    List<ScenarioAction> actions = new ArrayList<>();

    // Start with the 2nd day of the oldest invoice month.
    // This simulates real-world behavior where daily billing runs on the 2nd of each month
    // to close the previous month's invoices
    LocalDate billingDate = today().withDayOfMonth(2).minusMonths(invoices.size() - 1);

    // Add billing actions for each month until we reach today
    // This is necessary for the edge case when today is the first day of the month - then we don't
    // want to run dailyBilling for the 2nd of the month because that date is in the future.
    while (billingDate.isBefore(today())) {
      actions.add(new RunDailyBillingAction(org, billingDate));
      billingDate = billingDate.plusMonths(1);
    }

    // Add today's billing action
    actions.add(new RunDailyBillingAction(org, today()));

    return actions;
  }

  /**
   * This mode of running daily billing is useful to replicate real life billing scenarios more
   * closely. For example, it allows to create mid month payments due to MOB limit being reached.
   * Use it cautiously as it adds about 50% of execution time compared to the dailyBilling running
   * in {@link DailyBillingRunMode#MONTHLY} mode.
   */
  private List<ScenarioAction> createDailyBillingActionsDaily(
      OrgContainer org, List<ScenarioInvoice> invoices) {
    LocalDate startDate = today().withDayOfMonth(1).minusMonths(invoices.size() - 1);
    LocalDate endDate = today().plusDays(1);

    return startDate
        .datesUntil(endDate)
        .map(date -> new RunDailyBillingAction(org, date))
        .collect(java.util.stream.Collectors.toList());
  }

  private List<? extends ScenarioAction> createActivationCodeActions(
      OrgContainer payingOrg, List<ScenarioOpportunity> opportunities) {
    return opportunities.stream()
        .map(
            opp -> {
              LocalDate applyOn =
                  requireNonNullElse(
                      opp.applyOn(),
                      opp.credits().stream()
                          .map(ScenarioOpportunityCredit::startDate)
                          .min(Comparator.naturalOrder())
                          .orElseThrow());

              return new ApplyActivationCodeAction(payingOrg, opp, applyOn);
            })
        .toList();
  }

  /**
   * @return The first invoice start date.
   */
  private LocalDate getOrgCreationDate(List<ScenarioInvoice> invoices) {
    return today().withDayOfMonth(1).minusMonths(invoices.size() - 1);
  }

  private LocalDate today() {
    return LocalDate.ofInstant(clock.instant(), ZoneOffset.UTC);
  }

  private void validateRequest(Scenario scenario) {
    if (!scenario.linkedOrgs().isEmpty() && scenario.opportunities().isEmpty()) {
      throw new IllegalArgumentException(
          "Orgs cannot be linked without a sales-sold credit available");
    }

    LocalDate orgCreationDate = getOrgCreationDate(scenario.invoices());

    if (!scenario.linkedOrgs().isEmpty()) {
      for (int i = 0; i < scenario.linkedOrgs().size(); i++) {
        ScenarioLinkedOrg linkedOrg = scenario.linkedOrgs().get(i);

        if (linkedOrg.linkOn() == null) {
          continue;
        }
        if (linkedOrg.linkOn().isBefore(orgCreationDate)) {
          throw new IllegalArgumentException(
              String.format(
                  "Linked org [%d] has linkOn before the date of the main org first invoice. Please"
                      + " add more invoices or change the linkOn date",
                  i));
        }
        if (linkedOrg.linkOn().isBefore(getOrgCreationDate(linkedOrg.invoices()))) {
          throw new IllegalArgumentException(
              String.format(
                  "Linked org [%d] has linkOn after the date of the linked org first invoice."
                      + " Please add more invoices or change the linkOn date",
                  i));
        }
      }
    }

    if (scenario.selfServePaymentMethod() != null
        && scenario.selfServePaymentMethod().applyOn() != null
        && scenario.selfServePaymentMethod().applyOn().isBefore(orgCreationDate)) {
      throw new IllegalArgumentException(
          "Self-serve payment method has applyOn before the date of the first invoice. Please add"
              + " more invoices or change the applyOn date");
    }

    for (ScenarioOpportunity opp : scenario.opportunities()) {
      if (opp.applyOn() != null && opp.applyOn().isBefore(orgCreationDate)) {
        throw new IllegalArgumentException(
            "Opportunity has applyOn before the date of the first invoice. Please add more invoices"
                + " or change the applyOn date");
      }

      for (ScenarioOpportunityCredit credit : opp.credits()) {
        if (credit.startDate().isBefore(orgCreationDate)) {
          throw new IllegalArgumentException(
              "Credit has startDate before the date of the first invoice. Please add more invoices"
                  + " or change the startDate");
        }
      }
    }
  }
}
