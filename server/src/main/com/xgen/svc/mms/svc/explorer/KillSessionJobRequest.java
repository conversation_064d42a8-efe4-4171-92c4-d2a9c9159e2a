package com.xgen.svc.mms.svc.explorer;

import static com.xgen.cloud.common.explorer._public.model.DataExplorerOpType.KILL_SESSION;
import static com.xgen.cloud.common.model._public.error.CommonErrorCode.INVALID_PARAMETER;
import static com.xgen.cloud.common.model._public.error.CommonErrorCode.SERVER_ERROR;
import static com.xgen.svc.mms.svc.explorer.DataExplorerSvc.parseExtendedJSON;

import com.mongodb.BasicDBObject;
import com.xgen.cloud.activity._public.model.event.HostAudit;
import com.xgen.cloud.activity._public.model.event.HostEvent;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.explorer._public.model.DataExplorerOpType;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.util._public.util.AgentVersion;
import com.xgen.svc.explorer.jobs.GetDataExplorerOpJob;
import com.xgen.svc.mms.model.explorer.Parameters;
import com.xgen.svc.mms.svc.explorer.processor.AbstractProcessor;
import java.util.Objects;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class KillSessionJobRequest extends JobRequest {
  public static final DataExplorerOpType OP_TYPE = KILL_SESSION;
  private static final Logger LOG = LoggerFactory.getLogger(KillSessionJobRequest.class);

  private final KillSessionQueryProcessor _queryProcessor;

  private final Parameters _parameters;
  private final AuditInfo _auditInfo;

  public KillSessionJobRequest(final DataExplorerSvc pDataExplorerSvc, final Parameters pParameters)
      throws SvcException {
    super(pDataExplorerSvc, pParameters);

    _parameters = pParameters;
    _auditInfo = pParameters._auditInfo;
    _queryProcessor = new KillSessionQueryProcessor(pParameters);
  }

  @Override
  protected String fieldsToString() {
    return super.fieldsToString() + ", query='" + _queryProcessor.getProcessedOption() + "'";
  }

  @Override
  protected DataExplorerOpType getOpType() {
    return OP_TYPE;
  }

  @Override
  public void preSubmitJob() throws SvcException {
    final AgentVersion agentVersion =
        getDataExplorerSvc().getAutomationAgentSupportSvc().getMinAgentVersion(_parameters._group);

    if (!AgentVersionUtil.checkMinimumKillSessionAutomationAgentVersion(agentVersion)) {
      LOG.info(
          "Invalid automation agent version {} operation by userId={} for groupId={}: automation"
              + " agent version must be at least {}",
          OP_TYPE.getName(),
          _parameters._appUser.getId(),
          _parameters._group.getId(),
          AgentVersionUtil.getMinimumKillSessionAutomationAgentVersion());
      throw new SvcException(SERVER_ERROR);
    }
  }

  @Override
  protected GetDataExplorerOpJob.OperationBuilder createJobOperationBuilder() {
    return new GetDataExplorerOpJob.OperationBuilder().query(_queryProcessor.getProcessedOption());
  }

  @Override
  public void postSubmitJob() {
    final HostAudit.Builder builder =
        new HostAudit.Builder(
            HostEvent.Type.ATTEMPT_KILLSESSION_AUDIT,
            _parameters._group.getId(),
            getSelectedHost().getId());
    builder.auditInfo(_auditInfo);
    builder.hostnameAndPort(getSelectedHost().getHostnameAndPort());
    builder.userAlias(getSelectedHost().getUserAlias());
    builder.hostTypeIds(getSelectedHost().getHostTypes());
    builder.replicaSetId(getSelectedHost().getReplicaSetId());

    try {
      getDataExplorerSvc().getAuditSvc().saveAuditEvent(builder.build());
    } catch (Exception e) {
      LOG.error("Failed to add audit event for killSession", e);
    }
  }

  // -------------------- Job-specific Processors ------------------------------
  private static class KillSessionQueryProcessor extends AbstractProcessor {
    private KillSessionQueryProcessor(final Parameters pParameters) throws SvcException {
      super(
          pParameters,
          LoggerFactory.getLogger(KillSessionJobRequest.KillSessionQueryProcessor.class));
    }

    @Override
    protected BasicDBObject process(final Parameters pParameters) throws SvcException {
      if (Objects.isNull(pParameters._query)) {
        _LOG.error(
            "Null query string for {} operation by userId={} for groupId={}",
            OP_TYPE.getName(),
            pParameters._appUser.getId(),
            pParameters._group.getId());
        throw new SvcException(INVALID_PARAMETER);
      }

      final String queryTrimmed = pParameters._query.trim();
      final BasicDBObject dbObjectQuery = parseExtendedJSON(queryTrimmed);
      if (Objects.isNull(dbObjectQuery)) {
        _LOG.error(
            "Invalid JSON query string for {} operation by userId={} for groupId={}: {}",
            OP_TYPE.getName(),
            pParameters._appUser.getId(),
            pParameters._group.getId(),
            pParameters._query);
        throw new SvcException(INVALID_PARAMETER, OP_TYPE, queryTrimmed);
      }

      if (!dbObjectQuery.containsField("lsid")) {
        LOG.error(
            "Invalid query document for {} operation by userId={} for groupId={}: must contain"
                + " 'lsid' field",
            OP_TYPE.getName(),
            pParameters._appUser.getId(),
            pParameters._group.getId());
        throw new SvcException(INVALID_PARAMETER);
      }

      return dbObjectQuery;
    }
  }
}
