package com.xgen.svc.mms.model.performanceadvisor;

import com.xgen.svc.core.model.template.Template;

/** Email templates for Auto-indexing. */
public enum AutoIndexingTemplate implements Template {
  AUTO_INDEXING_INDEX_BUILD_FAILED,
  AUTO_INDEXING_INDEX_BUILD_CREATED,
  AUTO_INDEXING_INDEX_BUILD_SUCCESS,
  AUTO_INDEXING_INDEX_BUILD_SLOW,
  AUTO_INDEXING_INDEX_BUILD_STALLED;

  @Override
  public String getTemplateName() {
    return "email/performanceadvisor/" + name().toLowerCase();
  }
}
