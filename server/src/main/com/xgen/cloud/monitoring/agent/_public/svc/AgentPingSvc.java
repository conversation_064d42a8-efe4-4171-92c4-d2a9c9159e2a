package com.xgen.cloud.monitoring.agent._public.svc;

import com.xgen.cloud.monitoring.agent._private.dao.AgentPingDao;
import com.xgen.svc.mms.model.agent.AgentPing;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import org.bson.types.ObjectId;

@Singleton
public class AgentPingSvc {

  private final AgentPingDao _agentPingDao;

  @Inject
  public AgentPingSvc(final AgentPingDao pAgentPingDao) {
    _agentPingDao = pAgentPingDao;
  }

  /**
   * Looks up the last time a ping was received from the agent for the specified group. If no ping
   * data exists, returns null. Here, we look at both the last ping time and the last config call
   * time and return the more recent of the two.
   *
   * @param pGroupId group to search for
   * @return the last ping time (UTC), or null if there isn't one
   */
  public Long findLastPingTimeByGroupId(final ObjectId pGroupId) {
    return _agentPingDao.findLastPingTimeByGroupId(pGroupId);
  }

  public AgentPing findByGroupId(final ObjectId pGroupId) {
    return _agentPingDao.findByGroupId(pGroupId);
  }

  /**
   * Increment the last ping ("p") field's timestamp and the ping count ("pc").
   *
   * @return true if this ping's time is strictly after the latest ping's time. I.e., this function
   *     returns false if a more recent ping started before this one.
   */
  public boolean incrementPing(final ObjectId pGroupId, final long pingTime) {
    return _agentPingDao.incrementPing(pGroupId, pingTime);
  }

  /**
   * Update the ping last processed timestamp
   *
   * @param pGroupId The Group
   * @param pingTime Note, this is the timestamp of the ping that we last processed, not the time at
   *     which we last processed a ping. For example, if we just finished processing a ping that
   *     arrived 5 minutes ago, this timestamp will be 5 minutes in the past.
   */
  public void updatePingLastProcessed(final ObjectId pGroupId, final long pingTime) {
    _agentPingDao.updatePingLastProcessed(pGroupId, pingTime);
  }

  /** Increment the conf. */
  public void incrementConf(final ObjectId pGroupId) {
    _agentPingDao.incrementConf(pGroupId);
  }
}
