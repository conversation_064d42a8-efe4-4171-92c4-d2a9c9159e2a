package com.xgen.cloud.monitoring.tsstrategy._private.dao;

import com.mongodb.BasicDBObject;
import com.mongodb.MongoException;
import com.mongodb.ReadPreference;
import com.mongodb.WriteConcern;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCursor;
import com.mongodb.client.model.BulkWriteOptions;
import com.mongodb.client.model.InsertOneModel;
import com.xgen.cloud.common.dao.base._public.impl.BaseDao;
import com.xgen.cloud.common.db.mongo._public.container.MongoClientContainer;
import com.xgen.cloud.common.db.mongo._public.index.MongoIndex;
import com.xgen.cloud.monitoring.tsstrategy._private.dao.AbstractDaoStrategy.DaoTypeGranularity;
import java.time.Instant;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import org.bson.codecs.configuration.CodecRegistry;
import org.slf4j.Logger;

/**
 * Dao for a timestamped collection or DB that represents the full timestamped namespace
 * (lightweight throw-away object).
 */
public class Dao<T> extends BaseDao<T> {

  private final Logger _logger;
  private final Instant _instant;
  private final boolean _isMongos;
  private final List<MongoIndex> _additionalIndexes;
  private final DaoTypeGranularity _daoTypeGranularity;

  protected Dao(
      final Logger logger,
      final MongoClientContainer container,
      final String connectionName,
      final String dbName,
      final String collectionName,
      final CodecRegistry codecRegistry,
      final Instant instant,
      boolean isMongos,
      final List<MongoIndex> additionalIndexes,
      final DaoTypeGranularity pDaoTypeGranularity) {
    super(container, connectionName, dbName, collectionName, codecRegistry);
    _logger = logger;
    _instant = instant;
    _isMongos = isMongos;
    _additionalIndexes = additionalIndexes;
    _daoTypeGranularity = pDaoTypeGranularity;
  }

  protected Dao(
      final Logger logger,
      final MongoClientContainer container,
      final String connectionName,
      final String dbName,
      final String collectionName,
      final CodecRegistry codecRegistry,
      final Instant instant,
      boolean isMongos,
      final List<MongoIndex> additionalIndexes,
      final Class<T> defaultEncoderClass,
      final DaoTypeGranularity pDaoTypeGranularity) {
    super(container, connectionName, dbName, collectionName, codecRegistry, defaultEncoderClass);
    _logger = logger;
    _instant = instant;
    _isMongos = isMongos;
    _additionalIndexes = additionalIndexes;
    _daoTypeGranularity = pDaoTypeGranularity;
  }

  @Override
  public List<MongoIndex> getIndexes() {
    final List<MongoIndex> indexes = super.getIndexes();
    indexes.add(new MongoIndex(MongoIndex.builder().key("_id")));
    indexes.addAll(_additionalIndexes);
    return indexes;
  }

  public Instant getInstant() {
    return _instant;
  }

  protected boolean isMongos() {
    return _isMongos;
  }

  protected Logger getLogger() {
    return _logger;
  }

  protected DaoTypeGranularity getDaoTypeGranularity() {
    return _daoTypeGranularity;
  }

  public String getFullName() {
    return getDbName() + "." + getCollectionName();
  }

  /**
   * If a mongoS, ensure the database and collection for the given policy and sample time are
   * sharded.
   *
   * @param pNumInitialChunks - only passed to command if > 0
   */
  public void ensureShardedCollection(final int pNumInitialChunks) throws InterruptedException {
    final boolean unique = false;
    final boolean createShardKeyIndex = true;
    final BasicDBObject shardKey = new BasicDBObject("_id", "hashed");

    // Ensures indexes, and therefore something on filesystem exists.
    ensureCollection();
    ensureSharded(_logger, shardKey, pNumInitialChunks, unique, createShardKeyIndex);
  }

  protected void ensureCollection() {
    getIndexes()
        .forEach(index -> getCollection().createIndex(index.getKeys(), index.getIndexOptions()));
  }

  public BulkWriteResult insert(final List<T> pMetrics) throws MongoException {
    final BulkWriteOptions options = new BulkWriteOptions();
    options.ordered(false);

    final List<InsertOneModel<T>> batch =
        pMetrics.stream().map(metric -> new InsertOneModel<>(metric)).collect(Collectors.toList());

    return getCollection().withWriteConcern(WriteConcern.ACKNOWLEDGED).bulkWrite(batch, options);
  }

  public FindIterable<T> find(final BasicDBObject pQuery, final ReadPreference pReadPreference) {
    final FindIterable<T> result = getCollection().withReadPreference(pReadPreference).find(pQuery);
    return result;
  }

  public FindIterable<T> find(
      final BasicDBObject pQuery,
      final BasicDBObject pProjection,
      final ReadPreference pReadPreference) {
    final FindIterable<T> result =
        getCollection().withReadPreference(pReadPreference).find(pQuery).projection(pProjection);
    return result;
  }

  public MongoCursor<T> forRollup(
      final BasicDBObject pQuery,
      final BasicDBObject pSort,
      final BasicDBObject pProjection,
      final ReadPreference pReadPreference,
      final int pCursorBatchSize) {
    return getCollection()
        .withReadPreference(pReadPreference)
        .find(pQuery)
        .projection(pProjection)
        .noCursorTimeout(true)
        .sort(pSort)
        .batchSize(pCursorBatchSize)
        .cursor();
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    @SuppressWarnings("unchecked")
    Dao<T> dao = (Dao<T>) o;
    return Objects.equals(dao.getFullName(), getFullName());
  }

  @Override
  public int hashCode() {
    return Objects.hashCode(getFullName());
  }
}
