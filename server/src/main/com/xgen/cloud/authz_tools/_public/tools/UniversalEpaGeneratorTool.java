package com.xgen.cloud.authz_tools._public.tools;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import jakarta.annotation.security.RolesAllowed;
import jakarta.ws.rs.*;
import java.lang.reflect.Method;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Tool for generating EPA (Endpoint Action) values for ALL MMS endpoints.
 * This ensures universal EPA coverage as required by the EPA/CFA project.
 */
public class UniversalEpaGeneratorTool {
  private static final Logger LOG = LoggerFactory.getLogger(UniversalEpaGeneratorTool.class);
  
  private static final String EPA_NAMESPACE_PREFIX = "epa.";
  private static final Set<String> HTTP_METHODS = Set.of("GET", "POST", "PUT", "PATCH", "DELETE", "HEAD", "OPTIONS");
  
  public static void main(String[] args) {
    List<String> argsList = Arrays.asList(args);
    
    if (argsList.contains("--help")) {
      printUsage();
      return;
    }
    
    if (argsList.contains("--check")) {
      checkEpaCoverage();
    } else if (argsList.contains("--generate")) {
      generateUniversalEpa();
    } else if (argsList.contains("--validate")) {
      validateEpaFormat();
    } else {
      printUsage();
    }
  }
  
  private static void printUsage() {
    System.out.println("""
        Universal EPA Generator Tool
        
        Usage:
        --check      Check EPA coverage across all MMS endpoints
        --generate   Generate EPA values for all MMS endpoints missing them
        --validate   Validate existing EPA format compliance
        --help       Show this help message
        
        Examples:
        bazel run //server/src/main/com/xgen/cloud/authz_tools:UniversalEpaGeneratorTool -- --check
        bazel run //server/src/main/com/xgen/cloud/authz_tools:UniversalEpaGeneratorTool -- --generate
        """);
  }
  
  /**
   * Checks EPA coverage across all MMS endpoints and reports missing EPAs.
   */
  private static void checkEpaCoverage() {
    LOG.info("=== Checking EPA Coverage for All MMS Endpoints ===");
    
    List<Method> allMmsEndpoints = findAllMmsEndpoints();
    List<Method> endpointsWithoutEpa = new ArrayList<>();
    List<Method> endpointsWithInvalidEpa = new ArrayList<>();
    
    for (Method method : allMmsEndpoints) {
      Auth auth = method.getAnnotation(Auth.class);
      if (auth == null || auth.endpointAction().isEmpty()) {
        endpointsWithoutEpa.add(method);
      } else if (!isValidEpaFormat(auth.endpointAction())) {
        endpointsWithInvalidEpa.add(method);
      }
    }
    
    // Report results
    LOG.info("Total MMS endpoints found: {}", allMmsEndpoints.size());
    LOG.info("Endpoints with EPA: {}", allMmsEndpoints.size() - endpointsWithoutEpa.size());
    LOG.info("Endpoints missing EPA: {}", endpointsWithoutEpa.size());
    LOG.info("Endpoints with invalid EPA: {}", endpointsWithInvalidEpa.size());
    
    if (!endpointsWithoutEpa.isEmpty()) {
      LOG.warn("Endpoints missing EPA:");
      endpointsWithoutEpa.forEach(method -> 
          LOG.warn("  - {}.{}", method.getDeclaringClass().getSimpleName(), method.getName()));
    }
    
    if (!endpointsWithInvalidEpa.isEmpty()) {
      LOG.warn("Endpoints with invalid EPA format:");
      endpointsWithInvalidEpa.forEach(method -> {
        Auth auth = method.getAnnotation(Auth.class);
        LOG.warn("  - {}.{}: {}", 
            method.getDeclaringClass().getSimpleName(), 
            method.getName(), 
            auth.endpointAction());
      });
    }
    
    if (endpointsWithoutEpa.isEmpty() && endpointsWithInvalidEpa.isEmpty()) {
      LOG.info("✅ All MMS endpoints have valid EPA values!");
    } else {
      LOG.error("❌ EPA coverage incomplete. Run with --generate to fix.");
    }
  }
  
  /**
   * Generates EPA values for all MMS endpoints that don't have them.
   */
  private static void generateUniversalEpa() {
    LOG.info("=== Generating EPA Values for All MMS Endpoints ===");
    
    List<Method> allMmsEndpoints = findAllMmsEndpoints();
    List<EpaGenerationResult> generationResults = new ArrayList<>();
    
    for (Method method : allMmsEndpoints) {
      Auth auth = method.getAnnotation(Auth.class);
      
      // Skip if EPA already exists and is valid
      if (auth != null && !auth.endpointAction().isEmpty() && isValidEpaFormat(auth.endpointAction())) {
        continue;
      }
      
      // Generate EPA for this endpoint
      String resourceType = determineResourceType(method);
      String className = method.getDeclaringClass().getSimpleName();
      String methodName = method.getName();
      String httpMethod = getHttpMethod(method);
      
      String generatedEpa = generateEpaValue(resourceType, className, methodName, httpMethod);
      
      generationResults.add(new EpaGenerationResult(
          method.getDeclaringClass().getName(),
          method.getName(),
          generatedEpa,
          resourceType
      ));
    }
    
    // Output generation results
    LOG.info("Generated EPA values for {} endpoints:", generationResults.size());
    for (EpaGenerationResult result : generationResults) {
      LOG.info("  @Auth(endpointAction = \"{}\", resourceType = \"{}\")", 
          result.epaValue, result.resourceType);
      LOG.info("  // {}.{}", result.className, result.methodName);
      LOG.info("");
    }
    
    if (generationResults.isEmpty()) {
      LOG.info("✅ All endpoints already have EPA values!");
    } else {
      LOG.info("Generated {} EPA values. Apply these to your @Auth annotations.", 
          generationResults.size());
    }
  }
  
  /**
   * Validates EPA format compliance for existing EPA values.
   */
  private static void validateEpaFormat() {
    LOG.info("=== Validating EPA Format Compliance ===");
    
    List<Method> allMmsEndpoints = findAllMmsEndpoints();
    List<String> invalidEpas = new ArrayList<>();
    
    for (Method method : allMmsEndpoints) {
      Auth auth = method.getAnnotation(Auth.class);
      if (auth != null && !auth.endpointAction().isEmpty()) {
        if (!isValidEpaFormat(auth.endpointAction())) {
          invalidEpas.add(String.format("%s.%s: %s", 
              method.getDeclaringClass().getSimpleName(),
              method.getName(),
              auth.endpointAction()));
        }
      }
    }
    
    if (invalidEpas.isEmpty()) {
      LOG.info("✅ All EPA values follow correct format!");
    } else {
      LOG.error("❌ Found {} invalid EPA formats:", invalidEpas.size());
      invalidEpas.forEach(epa -> LOG.error("  - {}", epa));
    }
  }
  
  /**
   * Finds all MMS endpoints that need EPA values.
   */
  private static List<Method> findAllMmsEndpoints() {
    // This would scan the classpath for methods with @UiCall, @RolesAllowed, or @Auth
    // For now, returning empty list - in real implementation, this would use reflection
    // to scan all classes in the MMS package structure
    
    // TODO: Implement classpath scanning similar to GenerateAuthzEndpointActionsTool
    return new ArrayList<>();
  }
  
  /**
   * Determines the resource type for an endpoint based on role sets or path patterns.
   */
  private static String determineResourceType(Method method) {
    // Check UiCall roles
    UiCall uiCall = method.getAnnotation(UiCall.class);
    if (uiCall != null) {
      return determineResourceTypeFromRoles(Arrays.asList(uiCall.roles()));
    }
    
    // Check RolesAllowed
    RolesAllowed rolesAllowed = method.getAnnotation(RolesAllowed.class);
    if (rolesAllowed != null) {
      // Convert string role names to RoleSet (simplified)
      return "project"; // Default fallback
    }
    
    // Fallback to path-based detection
    Path pathAnnotation = method.getAnnotation(Path.class);
    if (pathAnnotation != null) {
      String path = pathAnnotation.value();
      if (path.contains("org")) return "organization";
      if (path.contains("cluster")) return "cluster";
      if (path.contains("group") || path.contains("project")) return "project";
    }
    
    return "global"; // Default fallback
  }
  
  /**
   * Determines resource type from role sets.
   */
  private static String determineResourceTypeFromRoles(List<RoleSet> roles) {
    for (RoleSet role : roles) {
      String roleName = role.name();
      if (roleName.startsWith("ORG_")) return "organization";
      if (roleName.startsWith("GROUP_")) return "project";
      if (roleName.startsWith("GLOBAL_")) return "global";
    }
    return "project"; // Default
  }
  
  /**
   * Gets the HTTP method for a method.
   */
  private static String getHttpMethod(Method method) {
    if (method.isAnnotationPresent(GET.class)) return "GET";
    if (method.isAnnotationPresent(POST.class)) return "POST";
    if (method.isAnnotationPresent(PUT.class)) return "PUT";
    if (method.isAnnotationPresent(PATCH.class)) return "PATCH";
    if (method.isAnnotationPresent(DELETE.class)) return "DELETE";
    if (method.isAnnotationPresent(HEAD.class)) return "HEAD";
    if (method.isAnnotationPresent(OPTIONS.class)) return "OPTIONS";
    return "GET"; // Default
  }
  
  /**
   * Generates EPA value following the standard pattern.
   */
  private static String generateEpaValue(String resourceType, String className, String methodName, String httpMethod) {
    return String.format("%s%s.%s.%s.%s", 
        EPA_NAMESPACE_PREFIX, resourceType, className, methodName, httpMethod);
  }
  
  /**
   * Validates EPA format compliance.
   */
  private static boolean isValidEpaFormat(String epa) {
    Pattern epaPattern = Pattern.compile(
        "^epa\\.(organization|project|cluster|global)\\.[A-Za-z0-9_]+\\.[A-Za-z0-9_]+\\.(GET|POST|PUT|PATCH|DELETE|HEAD|OPTIONS)$");
    return epaPattern.matcher(epa).matches();
  }
  
  /**
   * Result of EPA generation for an endpoint.
   */
  private static record EpaGenerationResult(
      String className,
      String methodName, 
      String epaValue,
      String resourceType
  ) {}
}
