package com.xgen.cloud.user._private.svc;

import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.constants._public.model.user.UserType;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.user._public.model.AppUserErrorCode;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.organization._public.svc.OrganizationSvc;
import com.xgen.cloud.team._private.dao.TeamDao;
import com.xgen.cloud.team._public.model.Team;
import com.xgen.cloud.user._private.dao.UserDao;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.cloud.user._public.model.activity.UserAudit;
import com.xgen.cloud.user._public.model.activity.UserEvent;
import com.xgen.cloud.user._public.model.activity.UserEvent.Type;
import com.xgen.cloud.usergroupsync._public.sync.UserGroupSyncSvc;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.util.Date;
import java.util.List;
import org.bson.types.ObjectId;

@Singleton
public class TeamAccessSvc {
  private final TeamDao teamDao;
  private final UserDao userDao;
  private final AuditSvc auditSvc;
  private final UserGroupSyncSvc userGroupSyncSvc;
  private final OrganizationSvc organizationSvc;

  @Inject
  public TeamAccessSvc(
      TeamDao teamDao,
      UserDao userDao,
      AuditSvc auditSvc,
      UserGroupSyncSvc userGroupSyncSvc,
      OrganizationSvc organizationSvc) {
    this.teamDao = teamDao;
    this.userDao = userDao;
    this.auditSvc = auditSvc;
    this.userGroupSyncSvc = userGroupSyncSvc;
    this.organizationSvc = organizationSvc;
  }

  public void addUserToTeam(AppUser user, Team team, AuditInfo auditInfo) throws SvcException {
    addUserToTeamWithoutUserGroupSync(user, team, auditInfo);
    Organization organization = organizationSvc.findById(team.getOrgId());
    userGroupSyncSvc.syncCreateOrUpdateToUserGroup(organization, team.getId());
  }

  // This is explicitly used in the TeamSvc::create and TeamSvc::addUsers, since there's already a
  // sync call. User group sync should be used in all other instances
  public void addUserToTeamWithoutUserGroupSync(AppUser user, Team team, AuditInfo auditInfo)
      throws SvcException {
    if (user.getType() == UserType.API) {
      throw new SvcException(AppUserErrorCode.API_KEY_CANNOT_BELONG_TO_TEAM);
    }
    if (!user.isTeamMember(team)) {
      userDao.addTeamId(user, team.getId());
      auditTeamChange(UserEvent.Type.JOINED_TEAM, user, team, auditInfo);
    }
  }

  public void removeUserFromTeam(AppUser user, Team team, AuditInfo auditInfo) throws SvcException {
    if (userDao.findLocalUsersByTeamId(team.getId()).isEmpty()) {
      return;
    }
    removeUserFromTeamWithValidationBypass(user, team, auditInfo);
  }

  public void removeUserFromTeamWithValidationBypass(AppUser user, Team team, AuditInfo auditInfo) {
    removeUserFromTeamWithValidationAndSyncBypass(user, team, auditInfo);
    Organization organization = organizationSvc.findById(team.getOrgId());
    userGroupSyncSvc.syncCreateOrUpdateToUserGroup(organization, team.getId());
  }

  private void removeUserFromTeamWithValidationAndSyncBypass(
      AppUser user, Team team, AuditInfo auditInfo) {
    userDao.removeTeamId(user, team.getId());
    auditTeamChange(Type.REMOVED_FROM_TEAM, user, team, auditInfo);
  }

  public void removeAllTeamUsers(ObjectId teamId, AuditInfo auditInfo) {
    List<AppUser> users = userDao.findLocalUsersByTeamId(teamId);
    Team team = teamDao.findById(teamId);
    for (AppUser user : users) {
      removeUserFromTeamWithValidationBypass(user, team, auditInfo);
    }
  }

  public void removeAllTeamUsersWithSyncBypass(ObjectId teamId, AuditInfo auditInfo) {
    List<AppUser> users = userDao.findLocalUsersByTeamId(teamId);
    Team team = teamDao.findById(teamId);
    for (AppUser user : users) {
      removeUserFromTeamWithValidationAndSyncBypass(user, team, auditInfo);
    }
  }

  private void auditTeamChange(
      UserEvent.Type eventType, AppUser user, Team team, AuditInfo auditInfo) {
    UserAudit.Builder builder = new UserAudit.Builder(eventType);
    builder.teamId(team.getId());
    builder.teamName(team.getName());
    builder.orgId(team.getOrgId());
    builder.targetUsername(user.getUsername());
    builder.createdAt(new Date());
    builder.auditInfo(auditInfo);

    auditSvc.saveAuditEvent(builder.build());
  }
}
