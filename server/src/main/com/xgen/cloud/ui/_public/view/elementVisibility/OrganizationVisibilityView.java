package com.xgen.cloud.ui._public.view.visibility;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonInclude(Include.NON_NULL)
public abstract class OrganizationVisibilityView {
  @JsonProperty("identity_and_access")
  public OrganizationAccessVisibilityView access;

  @JsonProperty("billing")
  public OrganizationBillingVisibilityView billing;

  public OrganizationVisibilityView() {
    this.access = new OrganizationAccessVisibilityView();
    this.billing = new OrganizationBillingVisibilityView();
  }

  public OrganizationVisibilityView setTeams(final Boolean enabled) {
    access.setTeams(enabled);
    return this;
  }

  public OrganizationVisibilityView setUserGroups(final Boolean enabled) {
    access.setUserGroups(enabled);
    return this;
  }

  public OrganizationVisibilityView setPolicies(final Boolean enabled) {
    access.setPolicies(enabled);
    return this;
  }

  public OrganizationVisibilityView setCostExplorer(final Boolean enabled) {
    billing.setCostExplorer(enabled);
    return this;
  }

  public OrganizationVisibilityView setLinkedOrganizations(final Boolean enabled) {
    billing.setLinkedOrganizations(enabled);
    return this;
  }
}
