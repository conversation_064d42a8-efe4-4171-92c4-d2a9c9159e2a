package com.xgen.cloud.communication._public.model.microsoftTeams;

import com.xgen.cloud.communication._public.model.base.Credentials;
import com.xgen.cloud.communication._public.model.enums.ProviderType;
import java.time.Instant;
import org.bson.codecs.pojo.annotations.BsonCreator;
import org.bson.codecs.pojo.annotations.BsonDiscriminator;
import org.bson.codecs.pojo.annotations.BsonId;
import org.bson.codecs.pojo.annotations.BsonIgnore;
import org.bson.codecs.pojo.annotations.BsonProperty;
import org.bson.types.ObjectId;

/** Representation of microsoftTeams credentials */
@BsonDiscriminator(
    key = Credentials.PROVIDER_TYPE_FIELD,
    value = ProviderType.CREDENTIALS_MICROSOFT_TEAMS)
public class MicrosoftTeamsCredentials extends Credentials {
  @BsonIgnore
  @Override
  public ProviderType getProviderType() {
    return ProviderType.MICROSOFT_TEAMS;
  }

  @BsonCreator
  public MicrosoftTeamsCredentials(
      @BsonId final ObjectId id,
      @BsonProperty(CREATED_AT_FIELD) Instant createdAt,
      @BsonProperty(UPDATED_AT_FIELD) Instant updatedAt) {
    super(id, createdAt, updatedAt);
  }

  public MicrosoftTeamsCredentials() {
    super(new ObjectId(), Instant.now(), Instant.now());
  }
}
