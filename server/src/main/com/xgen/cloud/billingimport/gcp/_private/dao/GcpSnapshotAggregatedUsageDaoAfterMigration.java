package com.xgen.cloud.billingimport.gcp._private.dao;

import com.xgen.cloud.common.db.legacy._public.svc.MongoSvc;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

@Singleton
public class GcpSnapshotAggregatedUsageDaoAfterMigration extends GcpSnapshotAggregatedUsageDaoImpl {

  public static final String DB_NAME_AFTER_MIGRATION = "mmsdbcloudproviders";
  public static final String COLLECTION_NAME_AFTER_MIGRATION = "gcpSnapshotAggregatedUsage";

  @Inject
  public GcpSnapshotAggregatedUsageDaoAfterMigration(final MongoSvc pMongoSvc) {
    super(pMongoSvc, DB_NAME_AFTER_MIGRATION, COLLECTION_NAME_AFTER_MIGRATION);
  }
}
