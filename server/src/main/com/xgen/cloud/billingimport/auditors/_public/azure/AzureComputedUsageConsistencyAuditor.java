package com.xgen.cloud.billingimport.auditors._public.azure;

import static com.xgen.cloud.billingplatform.audit._public.model.BillingAuditErrorCode.AZURE_COMPUTED_DATA_USAGE_DISCREPANCY;
import static net.logstash.logback.argument.StructuredArguments.kv;

import com.google.common.base.Equivalence;
import com.google.common.collect.MapDifference;
import com.google.common.collect.Maps;
import com.xgen.cloud.billingimport.azure._private.dao.AzureAggregatedUsageDao;
import com.xgen.cloud.billingimport.azure._public.svc.AzureBillingImportDateTrackerSvc;
import com.xgen.cloud.billingimport.common._public.util.CloudProviderMetrics;
import com.xgen.cloud.billingplatform.audit._public.framework.UntypedBillingAuditor;
import com.xgen.cloud.billingplatform.audit._public.model.AuditFailureDetail;
import com.xgen.cloud.billingplatform.audit._public.model.AuditorOwner;
import com.xgen.cloud.billingplatform.audit._public.model.BillingAuditError;
import com.xgen.cloud.billingplatform.audit._public.model.BillingAuditError.Builder;
import com.xgen.cloud.billingplatform.audit._public.svc.IAuditorConfigSvc;
import com.xgen.cloud.common.constants._public.model.partners.PartnerType;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.time.Clock;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.stream.Stream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * This auditor ensures that the data we submit to metering is consistent with the usage we receive
 * from Azure.
 *
 * <p>The invariant is that the sum of all computed deltas must equal the most recent reported
 * quantity.
 *
 * <p>We only validate data up to the day before the report day. Two reporting events are sufficient
 * to detect any discrepancies.
 */
@Singleton
public class AzureComputedUsageConsistencyAuditor extends UntypedBillingAuditor {
  private static final Logger LOG =
      LoggerFactory.getLogger(AzureComputedUsageConsistencyAuditor.class);
  private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");

  private final AzureAggregatedUsageDao azureAggregatedUsageDao;
  private final AzureBillingImportDateTrackerSvc importDateTracker;
  private final Clock clock;
  public static String MONTH = "month";
  public static String REPORTED_DATE = "reported_date";
  public static String REPORTED_NOT_CALCULATED = "reported_not_calculated";
  public static String CALCULATED_NOT_REPORTED = "calculated_not_reported";
  public static String DIFFERENT_VALUE = "different_value";

  @Inject
  public AzureComputedUsageConsistencyAuditor(
      final IAuditorConfigSvc auditorConfigSvc,
      final AzureAggregatedUsageDao azureAggregatedUsageDao,
      final Clock clock,
      final AzureBillingImportDateTrackerSvc importDateTracker) {
    super(auditorConfigSvc);
    this.clock = clock;
    this.azureAggregatedUsageDao = azureAggregatedUsageDao;
    this.importDateTracker = importDateTracker;
  }

  /**
   * Main method to audit Azure usage data for consistency.
   *
   * <p>It will validate the current months and past month reported data.
   *
   * @return failure details
   */
  @Override
  public Optional<AuditFailureDetail> audit() {
    LOG.info("Audit begin");

    LocalDate currentMonthLastSuccessfulImport =
        importDateTracker.getCurrentMonthLastSuccessfulImport();
    LocalDate previousMonthLastSuccessfulImport =
        importDateTracker.getPreviousMonthLastSuccessfulImport();

    LOG.info(
        "Audit last successful dates {} {}",
        kv("current", currentMonthLastSuccessfulImport),
        kv("previous", previousMonthLastSuccessfulImport));

    YearMonth currentMonth = YearMonth.from(currentMonthLastSuccessfulImport);
    YearMonth pasthMonth = currentMonth.minusMonths(1);
    Stream<BillingAuditError> pastMonthError =
        verifyConsistency(previousMonthLastSuccessfulImport, pasthMonth);
    Stream<BillingAuditError> currentMonthError =
        verifyConsistency(currentMonthLastSuccessfulImport, currentMonth);

    List<BillingAuditError> errors = Stream.concat(pastMonthError, currentMonthError).toList();

    LOG.info("# errors: {}", errors.size());
    CloudProviderMetrics.updateDataValidationSuccessRate(
        getAuditorName(), PartnerType.AZURE, errors.isEmpty());

    return errors.isEmpty()
        ? Optional.empty()
        : Optional.of(new AuditFailureDetail(getAuditorName(), null, errors));
  }

  /**
   * Validates data is consistent for the given month and a report date.
   *
   * @param reportDate report date to validate
   * @param month month that we are validating.
   * @return
   */
  private Stream<BillingAuditError> verifyConsistency(LocalDate reportDate, YearMonth month) {
    // Skip if reported data is before today, we only validate today's reported data.

    if (reportDate.isBefore(LocalDate.now(clock))) {
      return Stream.empty();
    }
    LOG.info(
        "Verifying consistency for report date and month - {} {}",
        kv("reportDate", reportDate),
        kv("month", month));

    // Total usage reported by Azure
    Map<String, Number> reportedUsage =
        azureAggregatedUsageDao.getTotalReportedUsageByType(month, reportDate);

    // Total daily deltas computed by Mongodb.
    Map<String, Number> computedUsage =
        azureAggregatedUsageDao.getTotalComputedUsageByType(month, reportDate);

    LOG.info(
        "Finished computing {} {}",
        kv("reportedUsage", reportedUsage),
        kv("computedUsage", computedUsage));

    return findBillingErrors(month, reportDate, reportedUsage, computedUsage).stream();
  }

  private static <T extends Number> Equivalence<T> precisionEquality(final double precision) {
    return new Equivalence<>() {
      @Override
      protected boolean doEquivalent(final T a, final T b) {
        return Math.abs(a.doubleValue() - b.doubleValue()) < precision;
      }

      @Override
      protected int doHash(final T pT) {
        return pT.hashCode();
      }
    };
  }

  private static Optional<BillingAuditError> findBillingErrors(
      YearMonth month,
      LocalDate reportDate,
      Map<String, Number> reportedUsage,
      Map<String, Number> computedUsage) {
    MapDifference<String, Number> difference =
        Maps.difference(reportedUsage, computedUsage, precisionEquality(1.0E-5));
    List<String> reportedOnly =
        difference.entriesOnlyOnLeft().entrySet().stream().map(Entry::toString).toList();
    List<String> calculatedOnly =
        difference.entriesOnlyOnRight().entrySet().stream().map(Entry::toString).toList();
    List<String> differentValue =
        difference.entriesDiffering().entrySet().stream().map(Entry::toString).toList();

    if (reportedOnly.isEmpty() && calculatedOnly.isEmpty() && differentValue.isEmpty()) {
      return Optional.empty();
    }

    return Optional.of(
        new Builder(AZURE_COMPUTED_DATA_USAGE_DISCREPANCY)
            .addArg(MONTH, month)
            .addArg(REPORTED_DATE, formatter.format(reportDate))
            .addArg(REPORTED_NOT_CALCULATED, reportedOnly.toString())
            .addArg(CALCULATED_NOT_REPORTED, calculatedOnly.toString())
            .addArg(DIFFERENT_VALUE, differentValue.toString())
            .build());
  }

  @Override
  public AuditorOwner getOwner() {
    return AuditorOwner.CLOUD_PROVIDERS;
  }
}
