package com.xgen.cloud.brs.daemon._public.grid.job;

import com.mongodb.BasicDBList;
import com.mongodb.DBObject;
import com.xgen.cloud.brs.core._private.dao.ClusterStatusDao;
import com.xgen.cloud.brs.core._private.dao.JobDao;
import com.xgen.cloud.brs.core._public.model.ClusterStatus;
import com.xgen.cloud.brs.core._public.model.job.JobResult;
import com.xgen.cloud.brs.daemon._public.grid.Daemon;
import com.xgen.cloud.common.brs._public.model.SnapshotSchedule;
import com.xgen.cloud.common.util._public.time.TimeUtils2;
import org.bson.types.BSONTimestamp;
import org.slf4j.Logger;

public class ClusterSettingsUpdateJob extends ClusterJob {

  Logger _backupLog;

  public ClusterSettingsUpdateJob(DBObject jobObj, JobDao jobDao) {
    super(jobObj, jobDao);

    _backupLog = _status.getLogger();
  }

  @Override
  public JobResult doWork(Daemon daemon) throws InterruptedException {

    DBObject mmsRequestsObj = _status.getMMSRequests();
    if (mmsRequestsObj == null || !mmsRequestsObj.containsField("updates")) {
      _backupLog.warn("SettingsUpdateJob did not find any settings to update.");
      return JobResult.OK;
    }

    MMSRequests mmsRequests = new MMSRequests(mmsRequestsObj);
    ClusterStatusDao clusterStatusDao = daemon.getBackupInjector().getClusterStatusDao();
    ClusterStatus status = clusterStatusDao.getClusterStatus(_status.getClusterId());

    if (mmsRequests.isScheduleUpdate()) {
      _backupLog.info("Old schedule: {}", status.getSnapshotSchedule());
      SnapshotSchedule newSchedule = mmsRequests.getNewSchedule();
      BSONTimestamp newNextSnapshotTs = calculateNextSnapshot(status, newSchedule, false);
      if (newNextSnapshotTs != null) {
        newSchedule.setReferencePoint(newNextSnapshotTs.getTime());
      }

      clusterStatusDao.setSnapshotSchedule(_status.getClusterId(), newSchedule, newNextSnapshotTs);
      _backupLog.info("New schedule: {}", newSchedule);
    }

    if (mmsRequests.isFastForwardNextSnapshotRequested()) {
      final BSONTimestamp nextSnapshotTimestamp = _status.getNextSnapshotTimestamp();
      final SnapshotSchedule currentSnapshotSchedule = _status.getSnapshotSchedule();

      if (nextSnapshotTimestamp != null && currentSnapshotSchedule != null) {
        BSONTimestamp newNextSnapshotTs =
            SettingsUpdateJob.getNextSnapshotInTheFuture(
                _status.getNextSnapshotTimestamp(), currentSnapshotSchedule, _status.getLogger());

        clusterStatusDao.setSnapshotSchedule(
            _status.getClusterId(), currentSnapshotSchedule, newNextSnapshotTs);
        _backupLog.info(
            "Fast forward next snapshot: {} {}", currentSnapshotSchedule, newNextSnapshotTs);
      }
    }

    if (mmsRequests.isClusterSynced() && status.allPiecesSynced()) {
      _backupLog.info("Old next snapshot: {}", status.getNextSnapshotTimestamp());
      BSONTimestamp nextSnapshot =
          calculateNextSnapshot(status, status.getSnapshotSchedule(), true);
      clusterStatusDao.clusterSynced(_status.getClusterId(), nextSnapshot);
      _backupLog.info("New next snapshot: {}", nextSnapshot);
    }

    clusterStatusDao.finishedClusterSettingsUpdateJob(
        _status.getClusterId(), mmsRequests.getVersion());

    return JobResult.OK;
  }

  /**
   * If `pSchedule.referencePointTimeOfDaySeconds` is null, choose the next plausible snapshot time
   * based on the new SnapshotSchedule. If the `referencePointTimeOfDay` is specified, then choose
   * the next available version of that time. If, for example, the referencePointTimeOfDay refers to
   * 12pm and next snapshot is scheduled for 11am, we should schedule it to be for today at 12pm If
   * on the other hand, the referencePointTimeOfDay refers to 12pm and the next snapshot is
   * scheduled for 1pm, we should schedule it for tomorrow at 12pm
   *
   * <p>Note: There's a similar method for replica sets in SettingsUpdateJob. The primary difference
   * being in the initial sync case.
   */
  public static BSONTimestamp calculateNextSnapshot(
      ClusterStatus pStatus, SnapshotSchedule pSchedule, boolean isSync) {
    Integer referenceTimeOfDay = pSchedule.getReferencePointTimeOfDaySeconds();
    Integer referenceTodaySeconds = null;
    if (referenceTimeOfDay != null) {
      // if referenceTimeOfDay is set, we use it to calculate the next snapshot and update
      // "schedule.reference"
      referenceTodaySeconds =
          (int) (TimeUtils2.getStartOfTodayInMillis() / 1000L) + referenceTimeOfDay;
    }

    return calculateNextSnapshot(pStatus, pSchedule, isSync, referenceTodaySeconds);
  }

  public static BSONTimestamp calculateNextSnapshot(
      ClusterStatus pStatus,
      SnapshotSchedule pSchedule,
      boolean isSync,
      Integer referenceTodaySeconds) {
    pStatus
        .getLogger()
        .info(
            "Calculating next snapshot timestamp. isSync: {}, referenceTodaySeconds: {}",
            isSync,
            referenceTodaySeconds);

    int targetNextSnapshotTime;
    if (isSync) {
      // start with now for initial syncs

      targetNextSnapshotTime = (int) (System.currentTimeMillis() / 1000);
    } else {
      if (pStatus.getLastSnapshotTimestamp() != null) {
        targetNextSnapshotTime =
            pStatus.getLastSnapshotTimestamp().getTime() + pSchedule.getSecondsBetweenSnapshots();

        /*
         * nextSnapshot should not move backward.  Make sure nextSnapshot >= previously planned nextSnapshotTime
         */
        if (targetNextSnapshotTime < pStatus.getNextSnapshotTimestamp().getTime()) {
          targetNextSnapshotTime = pStatus.getNextSnapshotTimestamp().getTime();
        }
      } else if (pStatus.getNextSnapshotTimestamp() != null) {
        targetNextSnapshotTime = pStatus.getNextSnapshotTimestamp().getTime();
      } else if (pStatus.isWTCheckpoint()) {
        // Is WT checkpoint cluster. Set to "now".
        final int nowInSeconds = (int) (System.currentTimeMillis() / 1000L);
        targetNextSnapshotTime = nowInSeconds;
      } else {
        pStatus
            .getLogger()
            .info(
                "Initial sync not yet complete, next snapshot time is not set yet. Returning"
                    + " null.");
        return null;
      }
    }
    pStatus.getLogger().info("TargetNextSnapshotTime computed as: {}", targetNextSnapshotTime);

    if (referenceTodaySeconds == null) {
      pStatus.getLogger().info("Returning next snapshot time as : {}", targetNextSnapshotTime);
      return new BSONTimestamp(targetNextSnapshotTime, 1);
    }

    if (targetNextSnapshotTime <= referenceTodaySeconds) {
      pStatus.getLogger().info("Returning next snapshot time as : {}", referenceTodaySeconds);
      return new BSONTimestamp(referenceTodaySeconds, 1);
    }

    pStatus
        .getLogger()
        .info(
            "Incrementing referenceTodaySeconds: {}, by the snapshot schedule: {} to catch up to"
                + " targetNextSnapshotTime : {}",
            referenceTodaySeconds,
            pSchedule.getSecondsBetweenSnapshots(),
            targetNextSnapshotTime);
    while (targetNextSnapshotTime > referenceTodaySeconds) {
      referenceTodaySeconds += pSchedule.getSecondsBetweenSnapshots();
    }

    pStatus.getLogger().info("Returning next snapshot time as : {}", referenceTodaySeconds);
    return new BSONTimestamp(referenceTodaySeconds, 1);
  }

  @Override
  public void kill() {}

  public static class MMSRequests {
    private final DBObject _mmsRequestsDoc;

    private boolean _scheduleUpdate;
    private SnapshotSchedule _newSchedule;

    private boolean _fastForwardNextSnapshot;

    private boolean _clusterSynced;

    @SuppressWarnings("unchecked")
    public MMSRequests(DBObject mmsRequestsDoc) {
      _mmsRequestsDoc = mmsRequestsDoc;
      if (!_mmsRequestsDoc.containsField("updates")) return;

      BasicDBList actionList = (BasicDBList) mmsRequestsDoc.get("updates");
      for (DBObject action : actionList.toArray(new DBObject[actionList.size()])) {
        switch ((String) action.get("action")) {
          case "schedule":
            _scheduleUpdate = true;
            _newSchedule = SnapshotSchedule.fromDBObject((DBObject) action.get("newSchedule"));
            break;

          case "fastForwardNextSnapshot":
            _fastForwardNextSnapshot = true;
            break;

          case "cluster synced":
            _clusterSynced = true;
            break;

          default:
            throw new RuntimeException(
                String.format("Unknown MMSRequest action %s", action.get("action")));
        }
      }
    }

    public int getVersion() {
      return (int) _mmsRequestsDoc.get("version");
    }

    public boolean isScheduleUpdate() {
      return _scheduleUpdate;
    }

    public boolean isFastForwardNextSnapshotRequested() {
      return _fastForwardNextSnapshot;
    }

    public SnapshotSchedule getNewSchedule() {
      return _newSchedule;
    }

    public boolean isClusterSynced() {
      return _clusterSynced;
    }
  }
}
