mongo:
  event-service:
    mongoUri: 'mongodb+srv://MONGO_EVENT_SERVICE_USERNAME:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8'
service:
  maxInboundMessageSize: 16777216 # 16 * 1024 * 1024 = 16 MiB
authn:
  internalClient:
    id: 'mdb_ic_id_67e5b6e5b464af141a831cff'
    secret: '(secret)AUTHN_INTERNAL_CLIENT_SECRET'
config:
  sdk:
    loadConfigServiceProperties: true
eventbus:
  subscriber:
    pollingEnabled: true
    profile: aws
    awsRegion: 'us-east-1'
    awsRoleArn: "arn:aws:iam::************:role/event-service-resources-staging/event-service-queue-producer-staging-aws-us-east-1"
    kinesis:
      streamName: "event-service-stream-staging-aws-us-east-1"
sentry:
  enabled: true
  sampleRate: 1.0
  dsn: (secret)SENTRY_DSN
  maxCacheItems: 300
  maxQueueSize: 300
