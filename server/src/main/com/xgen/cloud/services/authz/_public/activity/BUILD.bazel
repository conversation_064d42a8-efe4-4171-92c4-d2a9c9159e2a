load("//server/src/main:rules.bzl", "main_library")

main_library(
    name = "activity",
    srcs = glob(["**/*.java"]),
    deny_warnings = True,
    enable_nullaway = False,  # mapstruct generated source does not carry over @Nullable annotation:  TagsModifiedEvent$TagsModifiedEventMapperImpl.java:16: error: [NullAway]
    include_default_deps = False,
    plugins = ["//third_party:mapstruct_plugin"],
    deps = [
        "//server/src/main/com/xgen/cloud/common/event",
        "//server/src/main/com/xgen/cloud/common/mapstruct/grpc",
        "//systems/events:event_schemas_java_proto",  # keep
        "@com_google_protobuf//java/core",
        "@maven//:com_fasterxml_jackson_core_jackson_annotations",
        "@maven//:jakarta_annotation_jakarta_annotation_api",
        "@maven//:org_mapstruct_mapstruct",
    ],
)
