package com.xgen.cloud.services.core.auth._public.jwt;

import static com.xgen.cloud.common.constants._public.model.resources.ResourceTypeConstants.GLOBAL;
import static com.xgen.cloud.common.constants._public.model.resources.ResourceTypeConstants.ORGANIZATION;
import static com.xgen.cloud.common.constants._public.model.resources.ResourceTypeConstants.PROJECT;
import static com.xgen.cloud.common.dao.base._public.util.BsonUtils.Static.oid;

import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.authz._public.model.AuthzRequirement;
import com.xgen.cloud.common.authz._public.model.PrincipalType;
import com.xgen.cloud.common.authz._public.model.ResourceTypeId;
import com.xgen.cloud.common.constants._public.model.actions.ActionConstants;
import com.xgen.cloud.common.constants._public.model.resources.ResourceTypeConstants;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.services.core.auth._public.extractor.ResourceIdExtractorSvc;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.util.HashMap;
import java.util.Map;

/**
 * Converts an auth annotation + request to a map from request type to list of {@link
 * AuthzRequirement} which can more readily be used for authorization
 *
 * @param <T> request type, required to determine which extractor to use to get resource ids
 */
@Singleton
public class AuthzRequirementsSvc<T> {

  /**
   * a "magic number" authz services uses for global resource identifiers
   *
   * <p>see also {@link com.xgen.cloud.services.authz._private.model.ResourceId}
   */
  private static final Map<ResourceTypeConstants, String> DEFAULT_GLOBAL_RESOURCE =
      Map.of(GLOBAL, oid(1).toHexString());

  private final ResourceIdExtractorSvc<T> resourceIdExtractorSvc;

  @Inject
  public AuthzRequirementsSvc(final ResourceIdExtractorSvc<T> resourceIdExtractorSvc) {
    this.resourceIdExtractorSvc = resourceIdExtractorSvc;
  }

  /**
   * Converts an auth annotation + request to a map from request type to {@link AuthzRequirement}
   *
   * @param authAnnotation auth annotation to parse requirements from
   * @param request request to fetch relevant resource ids from
   * @return map from request type to auth requirement
   * @throws SvcException if parsing fails, extraction fails, or required resource ids are missing
   */
  public Map<PrincipalType, AuthzRequirement> parseRequirements(
      final Auth authAnnotation, final T request) throws SvcException {
    final Map<PrincipalType, AuthzRequirement> requirements = new HashMap<>();

    // Handle service authorization (S2S) - still uses ActionConstants
    if (authAnnotation.serviceAuthorization()) {
      final ActionConstants serviceAction =
          authAnnotation.serviceAction() != ActionConstants.NONE
              ? authAnnotation.serviceAction()
              : authAnnotation.action();
      requirements.put(PrincipalType.SERVICE, getAuthzRequirements(request, serviceAction));
    }

    // Handle initiator authorization - use EPA/CFA if present, otherwise fall back to ActionConstants
    if (authAnnotation.initiatorAuthorization()) {
      if (!authAnnotation.endpointAction().isEmpty()) {
        // Use EPA for MMS endpoints
        requirements.put(
            PrincipalType.INITIATOR,
            getAuthzRequirementsForEndpointAction(request, authAnnotation));
      } else {
        // Fall back to ActionConstants for legacy endpoints
        requirements.put(
            PrincipalType.INITIATOR, getAuthzRequirements(request, authAnnotation.action()));
      }
    }
    return requirements;
  }

  private AuthzRequirement getAuthzRequirements(final T request, final ActionConstants action)
      throws SvcException {
    final Map<ResourceTypeConstants, String> resourceIds = getResourceIdMap(request, action);
    return new AuthzRequirement(action, resourceIds);
  }

  /**
   * Creates AuthzRequirement for EPA-based (string action) authorization used by MMS endpoints.
   * This method extracts resource IDs based on the resourceType specified in the Auth annotation.
   *
   * EPA and CFA are part of the same authorization - CFA is the parent of EPA with the
   * relationship established in the Cedar schema. Both values are used together in authorization.
   */
  private AuthzRequirement getAuthzRequirementsForEndpointAction(
      final T request, final Auth authAnnotation) throws SvcException {
    final String endpointAction = authAnnotation.endpointAction();
    final String customerFacingAction = authAnnotation.customerFacingAction();
    final String resourceType = authAnnotation.resourceType();

    if (resourceType.isEmpty()) {
      throw new SvcException(
          CommonErrorCode.INVALID_ARGUMENT,
          "resourceType must be specified when using endpointAction");
    }

    // Convert string resourceType to ResourceTypeConstants
    final ResourceTypeConstants resourceTypeConstant = parseResourceType(resourceType);
    final Map<ResourceTypeConstants, String> resourceIds =
        getResourceIdMapForResourceType(request, resourceTypeConstant);

    // Create AuthzRequirement with EPA and CFA metadata
    // The authorization service will use both values according to Cedar schema relationships
    return AuthzRequirement.forEndpointAction(
        endpointAction, customerFacingAction, resourceTypeConstant, resourceIds);
  }

  /**
   * Converts string resource type to ResourceTypeConstants enum.
   */
  private ResourceTypeConstants parseResourceType(final String resourceType) throws SvcException {
    return switch (resourceType.toLowerCase()) {
      case "organization" -> ResourceTypeConstants.ORGANIZATION;
      case "project" -> ResourceTypeConstants.PROJECT;
      case "cluster" -> ResourceTypeConstants.CLUSTER;
      case "global" -> ResourceTypeConstants.GLOBAL;
      default -> throw new SvcException(
          CommonErrorCode.INVALID_ARGUMENT,
          "Unsupported resourceType: " + resourceType);
    };
  }

  /**
   * Gets a map from resource type to relevant resource ids for a given action and request. Some
   * actions require fetching resource ids for multiple resource types to do evaluation, such as
   * "project" actions requiring groupId and orgId to evaluate authorization
   *
   * @param request the request to extract the resource ids from
   * @param action the action to get relevant resource ids for
   * @return map from resource type to resource id
   * @throws SvcException if resource id fetching fails
   */
  private Map<ResourceTypeConstants, String> getResourceIdMap(
      final T request, final ActionConstants action) throws SvcException {
    if (action == ActionConstants.NONE) {
      return Map.of();
    }
    final String service = action.getService();
    return switch (action.getResourceType()) {
      case ORGANIZATION ->
          // Organization just requires the orgId since it's a top-level resource
          Map.of(ORGANIZATION, getResourceId(request, service, action.getResourceType()));
      case PROJECT, CLUSTER ->
          // Project & Cluster requires both groupId and orgId since it is a sub-resource of project
          Map.of(
              ORGANIZATION, getResourceId(request, service, ORGANIZATION),
              PROJECT, getResourceId(request, service, action.getResourceType()));
      case GLOBAL -> DEFAULT_GLOBAL_RESOURCE;
      default -> Map.of();
    };
  }

  /**
   * Gets resource ID map for EPA-based authorization using a specific resource type.
   * This follows the same hierarchy logic as ActionConstants but uses the provided resource type.
   */
  private Map<ResourceTypeConstants, String> getResourceIdMapForResourceType(
      final T request, final ResourceTypeConstants resourceType) throws SvcException {
    final String service = "cloud"; // EPA always uses cloud service
    return switch (resourceType) {
      case ORGANIZATION ->
          // Organization just requires the orgId since it's a top-level resource
          Map.of(ORGANIZATION, getResourceId(request, service, resourceType));
      case PROJECT, CLUSTER ->
          // Project & Cluster requires both groupId and orgId since it is a sub-resource of project
          Map.of(
              ORGANIZATION, getResourceId(request, service, ORGANIZATION),
              PROJECT, getResourceId(request, service, resourceType));
      case GLOBAL -> DEFAULT_GLOBAL_RESOURCE;
      default -> Map.of();
    };
  }

  /**
   * Extracts a resourceId for a given request, service, and resource type. Uses the service and
   * resourceType to create a ResourceTypeId which is used by the resource extractor to determine
   * how to fetch the id
   *
   * @param request request to extract the resourceId from
   * @param service service to use for the ResourceTypeId
   * @param resourceType resource type to use for the ResourceTypeId
   * @return ResourceId as a string
   * @throws SvcException if resource id is missing or resource id extraction fails
   */
  private String getResourceId(
      final T request, final String service, final ResourceTypeConstants resourceType)
      throws SvcException {
    return resourceIdExtractorSvc
        .getResourceId(request, new ResourceTypeId(service, resourceType.getType()))
        .orElseThrow(
            () ->
                new SvcException(
                    CommonErrorCode.BAD_REQUEST,
                    "Request requires the resource type {}",
                    resourceType));
  }
}
