package com.xgen.cloud.services.core.auth._public.jwt;

import com.xgen.cloud.authz.core._public.client.AuthorizationClient;
import com.xgen.cloud.common.authz._public.model.AuthzRequestInfo;
import com.xgen.cloud.common.authz._public.model.AuthzRequirement;
import com.xgen.cloud.common.constants._public.model.actions.ActionConstants;
import com.xgen.cloud.common.metrics._public.svc.PromMetricsSvc;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.SvcException;
import io.prometheus.client.Counter;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.security.Principal;

/** FGA AuthZ Service - performs online authorization by calling AuthZ Service for a given actor */
@Singleton
public class FgaAuthzSvc extends AuthzSvc<String> {
  private static final Counter AUTHORIZATION_ATTEMPTS_COUNTER =
      PromMetricsSvc.registerCounter(
          "iam_fga_authorization_attempt_total",
          "Total number of authorization attempts going through the FGA AuthZ logic",
          AuthConstants.ACTION_LABEL,
          AuthConstants.SERVICE_LABEL,
          AuthConstants.RESOURCE_TYPE_LABEL);
  private static final Counter AUTHORIZATION_SUCCESS_COUNTER =
      PromMetricsSvc.registerCounter(
          "iam_fga_authorization_success_total",
          "Total successful authorization attempts going through the FGA AuthZ logic",
          AuthConstants.ACTION_LABEL,
          AuthConstants.SERVICE_LABEL,
          AuthConstants.RESOURCE_TYPE_LABEL);
  private static final Counter AUTHORIZATION_FAILURE_COUNTER =
      PromMetricsSvc.registerCounter(
          "iam_fga_authorization_failure_total",
          "Total failed authorization attempts going through the FGA AuthZ logic",
          AuthConstants.ACTION_LABEL,
          AuthConstants.SERVICE_LABEL,
          AuthConstants.RESOURCE_TYPE_LABEL);
  private final AuthorizationClient authorizationClient;

  @Inject
  public FgaAuthzSvc(final AuthorizationClient authorizationClient) {
    this.authorizationClient = authorizationClient;
  }

  /**
   * @param principal Principal to validate permissions for
   * @param actorId String actor id associated with principal
   * @param requirement {@link AuthzRequirement} to validate against
   * @return boolean - true if authorized, false otherwise
   * @throws SvcException
   */
  @Override
  public boolean authorize(
      final Principal principal, final String actorId, final AuthzRequirement requirement)
      throws SvcException {

    if (requirement.isEndpointAction()) {
      return authorizeEndpointAction(actorId, (AuthzRequirement.EpaAuthzRequirement) requirement);
    }

    final ActionConstants action = requirement.action();
    incrementProm(AUTHORIZATION_ATTEMPTS_COUNTER, action);
    // AuthZ Service only needs the resource id for the action's primary resource type for
    // evaluation since it maintains a hierarchy of resources
    final String resourceId =
        requirement.resourceTypeToIds().getOrDefault(action.getResourceType(), "");
    if (resourceId.isEmpty()) {
      throw new SvcException(
          CommonErrorCode.BAD_REQUEST,
          "No ResourceId specified for action type {}",
          requirement.action().getResourceType());
    }
    final AuthzRequestInfo requestInfo = new AuthzRequestInfo(actorId, action, resourceId);
    final boolean authorized = authorizationClient.isAuthorized(requestInfo);
    incrementProm(
        authorized ? AUTHORIZATION_SUCCESS_COUNTER : AUTHORIZATION_FAILURE_COUNTER, action);
    return authorized;
  }

  /**
   * Handles authorization for EPA (Endpoint Action) requirements.
   */
  private boolean authorizeEndpointAction(
      final String actorId, final AuthzRequirement.EpaAuthzRequirement requirement)
      throws SvcException {
    final String endpointAction = requirement.getEndpointAction();
    final ResourceTypeConstants primaryResourceType = requirement.getPrimaryResourceType();

    // Get the resource ID for the primary resource type
    final String resourceId =
        requirement.resourceTypeToIds().getOrDefault(primaryResourceType, "");
    if (resourceId.isEmpty()) {
      throw new SvcException(
          CommonErrorCode.BAD_REQUEST,
          "No ResourceId specified for EPA action type {}",
          primaryResourceType);
    }

    // Create AuthzRequestInfo with EPA string
    final AuthzRequestInfo requestInfo = new AuthzRequestInfo(
        actorId,
        endpointAction,
        "cloud", // service is always "cloud" for MMS
        primaryResourceType.getType(),
        resourceId);

    final boolean authorized = authorizationClient.isAuthorized(requestInfo);

    // For EPA actions, we'll use a simplified metric approach since we don't have ActionConstants
    incrementPromForEpa(
        authorized ? AUTHORIZATION_SUCCESS_COUNTER : AUTHORIZATION_FAILURE_COUNTER,
        endpointAction,
        primaryResourceType);

    return authorized;
  }

  private void incrementProm(final Counter counter, final ActionConstants action) {
    PromMetricsSvc.incrementCounter(
        counter, action.getAction(), action.getService(), action.getResourceType().getType());
  }
}
