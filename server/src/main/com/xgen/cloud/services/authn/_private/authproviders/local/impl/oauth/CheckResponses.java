package com.xgen.cloud.services.authn._private.authproviders.local.impl.oauth;

import static com.xgen.cloud.services.authn._private.util.CheckResponseUtil.addDownstreamHeader;
import static com.xgen.cloud.services.authn._private.util.CheckResponseUtil.addUpstreamHeader;

import com.google.rpc.Status;
import io.envoyproxy.envoy.service.auth.v3.CheckResponse;
import io.envoyproxy.envoy.service.auth.v3.DeniedHttpResponse;
import io.envoyproxy.envoy.service.auth.v3.OkHttpResponse;
import io.envoyproxy.envoy.type.v3.HttpStatus;
import io.envoyproxy.envoy.type.v3.StatusCode;
import java.util.Map;
import org.apache.http.HttpHeaders;

public class CheckResponses {
  private CheckResponses() {}

  public static CheckResponse accessDenied(int statusCode) {
    return CheckResponse.newBuilder()
        .setStatus(Status.newBuilder().setCode(statusCode))
        .setDeniedResponse(
            DeniedHttpResponse.newBuilder()
                .setStatus(HttpStatus.newBuilder().setCode(StatusCode.forNumber(statusCode)))
                .build())
        .build();
  }

  public static CheckResponse empty() {
    return CheckResponse.newBuilder().build();
  }

  // This method should be called only in case when authentication was successful
  // MMS relies on OkHttpResponse as a flag that credentials were validated by AuthN
  public static CheckResponse ok() {
    return CheckResponse.newBuilder().setOkResponse(OkHttpResponse.newBuilder().build()).build();
  }

  public static CheckResponse okBearer(String accessToken) {
    var ok = OkHttpResponse.newBuilder();
    addUpstreamHeader(ok, HttpHeaders.AUTHORIZATION, "Bearer " + accessToken, false);
    return CheckResponse.newBuilder().setOkResponse(ok.build()).build();
  }

  public static CheckResponse okInitiator(String token) {
    var ok = OkHttpResponse.newBuilder();
    addUpstreamHeader(ok, "x-xgen-auth-token", token, false);
    return CheckResponse.newBuilder().setOkResponse(ok.build()).build();
  }

  public static CheckResponse unauthorized(Map<String, String> headers) {
    var denied =
        DeniedHttpResponse.newBuilder()
            .setStatus(HttpStatus.newBuilder().setCode(StatusCode.Unauthorized));
    headers.forEach((key, value) -> addDownstreamHeader(denied, key, value, false));
    return CheckResponse.newBuilder()
        .setStatus(Status.newBuilder().setCode(StatusCode.Unauthorized_VALUE))
        .setDeniedResponse(denied.build())
        .build();
  }

  /** Return invalid response caused by unexpected error */
  public static CheckResponse serverError() {
    return CheckResponse.newBuilder()
        .setStatus(Status.newBuilder().setCode(StatusCode.InternalServerError_VALUE))
        .setDeniedResponse(
            DeniedHttpResponse.newBuilder()
                .setStatus(HttpStatus.newBuilder().setCode(StatusCode.InternalServerError))
                .build())
        .build();
  }
}
