package com.xgen.cloud.organization._public.model;

import static com.xgen.cloud.organization._public.model.VercelIntegration.FieldDefs.ACCESS_TOKEN_FIELD;
import static com.xgen.cloud.organization._public.model.VercelIntegration.FieldDefs.CONFIGURATION_ID_FIELD;
import static com.xgen.cloud.organization._public.model.VercelIntegration.FieldDefs.CREATED_FIELD;
import static com.xgen.cloud.organization._public.model.VercelIntegration.FieldDefs.TEAM_ID_FIELD;
import static com.xgen.cloud.organization._public.model.VercelIntegration.FieldDefs.TEAM_NAME_FIELD;
import static com.xgen.cloud.organization._public.model.VercelIntegration.FieldDefs.VERCEL_EMAIL_FIELD;
import static com.xgen.cloud.organization._public.model.VercelIntegration.FieldDefs.VERCEL_USER_ID_FIELD;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xgen.cloud.common.model._public.annotation.GenEncryptMetadata;
import com.xgen.cloud.common.security._public.util.EncryptionUtils;
import java.util.Date;
import org.bson.types.ObjectId;

@SuppressWarnings("NullAway") // suppressed to enable nullaway to address another issue
public class VercelIntegration {
  public static final String DB_NAME = "mmsdbconfig";
  public static final String COLLECTION_NAME = "vercelIntegrations";

  private ObjectId id = ObjectId.get();

  @JsonProperty(VERCEL_EMAIL_FIELD)
  private String vercelEmail;

  @JsonProperty(VERCEL_USER_ID_FIELD)
  private String vercelUserId;

  @JsonProperty(ACCESS_TOKEN_FIELD)
  private String accessToken;

  @JsonProperty(CONFIGURATION_ID_FIELD)
  private String configurationId;

  @JsonProperty(CREATED_FIELD)
  private Date created;

  @JsonProperty(TEAM_ID_FIELD)
  private String teamId;

  @JsonProperty(TEAM_NAME_FIELD)
  private String teamName;

  public VercelIntegration(
      String pVercelEmail,
      String pVercelUserId,
      String pAccessToken,
      String pConfigurationId,
      String pTeamId,
      String pTeamName) {
    vercelEmail = pVercelEmail;
    vercelUserId = pVercelUserId;
    accessToken = encryptAccessToken(pAccessToken);
    configurationId = pConfigurationId;
    teamId = pTeamId;
    teamName = pTeamName;
  }

  public VercelIntegration() {}

  public String getVercelEmail() {
    return vercelEmail;
  }

  public void setVercelEmail(String pVercelEmail) {
    vercelEmail = pVercelEmail;
  }

  public String getVercelUserId() {
    return vercelUserId;
  }

  public void setVercelUserId(String pVercelUserId) {
    vercelUserId = pVercelUserId;
  }

  public String getTeamId() {
    return teamId;
  }

  public void setTeamId(String pTeamId) {
    teamId = pTeamId;
  }

  public String getTeamName() {
    return teamName;
  }

  public void setTeamName(String pTeamName) {
    teamName = pTeamName;
  }

  public String getUnencryptedAccessToken() {
    return decryptAccessToken(accessToken);
  }

  public String getAccessToken() {
    return accessToken;
  }

  public void setAccessToken(String pAccessToken) {
    accessToken = pAccessToken;
  }

  public String getConfigurationId() {
    return configurationId;
  }

  public void setConfigurationId(String pConfigurationId) {
    configurationId = pConfigurationId;
  }

  public Date getCreated() {
    return created;
  }

  public void setCreated(Date pCreated) {
    created = pCreated;
  }

  private String encryptAccessToken(String pAccessToken) throws IllegalStateException {
    try {
      @GenEncryptMetadata(
          DB = VercelIntegration.DB_NAME,
          Collection = VercelIntegration.COLLECTION_NAME,
          Field = ACCESS_TOKEN_FIELD)
      String encAccessToken = EncryptionUtils.genEncryptStr(pAccessToken);
      return encAccessToken;
    } catch (Exception pE) {
      throw new IllegalStateException(
          "Problem encrypting the Access Token. Error: " + pE.getMessage(), pE);
    }
  }

  private String decryptAccessToken(String pAccessToken) throws IllegalStateException {
    try {
      @GenEncryptMetadata(
          DB = VercelIntegration.DB_NAME,
          Collection = VercelIntegration.COLLECTION_NAME,
          Field = ACCESS_TOKEN_FIELD)
      String decryptAccessToken = EncryptionUtils.genDecryptStr(pAccessToken);
      return decryptAccessToken;
    } catch (Exception pE) {
      throw new IllegalStateException(
          String.format(
              "Problem decrypting the Access Token: %s, error message: %s, error: %s",
              pAccessToken, pE.getMessage(), pE));
    }
  }

  public static class FieldDefs {
    public static final String VERCEL_EMAIL_FIELD = "vercelEmail";
    public static final String VERCEL_USER_ID_FIELD = "vercelUserId";
    public static final String ACCESS_TOKEN_FIELD = "accessToken";
    public static final String CONFIGURATION_ID_FIELD = "configurationId";
    public static final String TEAM_ID_FIELD = "teamId";
    public static final String TEAM_NAME_FIELD = "teamName";
    public static final String CREATED_FIELD = "created";
  }
}
