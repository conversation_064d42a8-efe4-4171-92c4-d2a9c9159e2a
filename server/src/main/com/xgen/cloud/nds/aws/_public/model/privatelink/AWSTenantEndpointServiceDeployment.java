package com.xgen.cloud.nds.aws._public.model.privatelink;

import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;
import com.xgen.cloud.common.mongo._public.mongo.DbUtils;
import com.xgen.cloud.nds.cloudprovider._public.model.privatelink.TenantEndpointServiceDeployment;
import com.xgen.cloud.nds.common._public.model.OperationalLimits;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import org.bson.types.ObjectId;

public class AWSTenantEndpointServiceDeployment extends TenantEndpointServiceDeployment {
  private final List<AWSTenantEndpointService> _endpointServices;

  public AWSTenantEndpointServiceDeployment() {
    super();
    _endpointServices = List.of();
  }

  protected AWSTenantEndpointServiceDeployment(
      final ObjectId pId,
      final int pNumDesiredEndpointServices,
      final Date pDeleteRequestedDate,
      final List<AWSTenantEndpointService> pEndpointServices) {
    super(pId, pNumDesiredEndpointServices, pDeleteRequestedDate);
    _endpointServices = pEndpointServices;
  }

  public AWSTenantEndpointServiceDeployment(
      final int pNumDesiredEndpointServices,
      final List<AWSTenantEndpointService> pEndpointServices) {
    super(new ObjectId(), pNumDesiredEndpointServices, null);
    _endpointServices = pEndpointServices;
  }

  public AWSTenantEndpointServiceDeployment(final BasicDBObject pDBObject) {
    super(pDBObject);
    _endpointServices =
        ((BasicDBList) pDBObject.get(FieldDefs.ENDPOINT_SERVICES))
            .stream()
                .map(BasicDBObject.class::cast)
                .map(AWSTenantEndpointService::new)
                .collect(Collectors.toList());
  }

  @Override
  public List<AWSTenantEndpointService> getEndpointServices() {
    return _endpointServices;
  }

  @Override
  public BasicDBObject toDBObject() {
    return super.toDBObject()
        .append(
            FieldDefs.ENDPOINT_SERVICES,
            getEndpointServices().stream()
                .map(AWSTenantEndpointService::toDBObject)
                .collect(DbUtils.toBasicDBList()));
  }

  public static AWSTenantEndpointServiceDeployment getDefaultDeployment(
      final OperationalLimits pOperationalLimits) {
    final int numDesiredEndpointServices =
        pOperationalLimits.getAwsNumTenantPrivateEndpointServices().getMinOrDefault().get();
    // Generate num of desired endpoint services in INITIATING state.
    final List<AWSTenantEndpointService> endpointServices = new ArrayList<>();
    for (int i = 0; i < numDesiredEndpointServices; i++) {
      endpointServices.add(new AWSTenantEndpointService());
    }
    return new AWSTenantEndpointServiceDeployment(numDesiredEndpointServices, endpointServices);
  }

  @Override
  public Builder toBuilder() {
    return new Builder(this);
  }

  public static Builder builder() {
    return new Builder();
  }

  public static final class Builder extends TenantEndpointServiceDeployment.Builder<Builder> {
    public List<AWSTenantEndpointService> _endpointServices;

    public Builder() {
      super();
      _endpointServices = List.of();
    }

    public Builder(final AWSTenantEndpointServiceDeployment pTenantEndpointServiceDeployment) {
      super(pTenantEndpointServiceDeployment);
      _endpointServices = pTenantEndpointServiceDeployment.getEndpointServices();
    }

    public Builder endpointServices(final List<AWSTenantEndpointService> pEndpointServices) {
      _endpointServices = pEndpointServices;
      return this;
    }

    @Override
    protected Builder self() {
      return this;
    }

    @Override
    public AWSTenantEndpointServiceDeployment build() {
      return new AWSTenantEndpointServiceDeployment(
          _id, _numDesiredEndpointServices, _deleteRequestedDate, _endpointServices);
    }
  }

  public static class FieldDefs {
    public static final String ENDPOINT_SERVICES = "endpointServices";
  }
}
