package com.xgen.cloud.nds.autoscaling.common._public.model;

import com.xgen.cloud.nds.autoscaling.common._public.model.ui.AutoScalingMemoryThresholdTypeView;
import com.xgen.cloud.nds.autoscaling.common._public.model.ui.CloudProviderConditionView;
import com.xgen.cloud.nds.autoscaling.common._public.model.ui.ComputeAutoScalingCPUThresholdView;
import com.xgen.cloud.nds.autoscaling.common._public.model.ui.ComputeAutoScalingConditionView;
import com.xgen.cloud.nds.autoscaling.common._public.model.ui.ComputeAutoScalingIntervalThresholdView;
import com.xgen.cloud.nds.autoscaling.common._public.model.ui.ComputeAutoScalingMemoryThresholdView;
import com.xgen.cloud.nds.autoscaling.common._public.model.ui.ComputeAutoScalingThresholdView;
import com.xgen.cloud.nds.autoscaling.common._public.model.ui.ComputeAutoScalingUptimeThresholdView;
import com.xgen.cloud.nds.autoscaling.common._public.model.ui.ComputeAutoScalingWTUsageThresholdView;
import com.xgen.cloud.nds.autoscaling.common._public.model.ui.InstanceSizeConditionView;
import com.xgen.cloud.nds.autoscaling.common._public.model.ui.SingleScalingCriterionView;
import com.xgen.cloud.nds.autoscaling.context._public.model.compute.SingleScalingCriteria;
import com.xgen.cloud.nds.autoscaling.context._public.model.compute.scalingcriteriacondition.CloudProviderCondition;
import com.xgen.cloud.nds.autoscaling.context._public.model.compute.scalingcriteriacondition.InstanceSizeCondition;
import com.xgen.cloud.nds.autoscaling.context._public.model.compute.scalingcriteriacondition.ScalingCriteriaCondition;
import com.xgen.cloud.nds.autoscaling.context._public.model.compute.thresholds.criteria.AutoScalingCPUMetricType;
import com.xgen.cloud.nds.autoscaling.context._public.model.compute.thresholds.criteria.AutoScalingCPUThresholdType;
import com.xgen.cloud.nds.autoscaling.context._public.model.compute.thresholds.criteria.AutoScalingMemoryThresholdType;
import com.xgen.cloud.nds.autoscaling.context._public.model.compute.thresholds.criteria.ComputeAutoScalingCPUThreshold;
import com.xgen.cloud.nds.autoscaling.context._public.model.compute.thresholds.criteria.ComputeAutoScalingIntervalThreshold;
import com.xgen.cloud.nds.autoscaling.context._public.model.compute.thresholds.criteria.ComputeAutoScalingMemoryThreshold;
import com.xgen.cloud.nds.autoscaling.context._public.model.compute.thresholds.criteria.ComputeAutoScalingThreshold;
import com.xgen.cloud.nds.autoscaling.context._public.model.compute.thresholds.criteria.ComputeAutoScalingUptimeThreshold;
import com.xgen.cloud.nds.autoscaling.context._public.model.compute.thresholds.criteria.ComputeAutoScalingWTUsageThreshold;
import com.xgen.cloud.nds.autoscaling.context._public.model.compute.thresholds.metrics.ThresholdMetrics;
import java.util.List;
import java.util.function.Predicate;
import java.util.stream.Collectors;

public final class UiViewConverter {

  private UiViewConverter() {}

  public static SingleScalingCriteria toSingleScalingCriterionModel(
      final SingleScalingCriterionView pUiCriterion) {
    final List<ComputeAutoScalingThreshold<? extends ThresholdMetrics>> thresholdList =
        pUiCriterion.getThresholds().stream()
            .map(UiViewConverter::toComputeAutoScalingThresholdModel)
            .collect(Collectors.toList());

    final List<ScalingCriteriaCondition> conditionList =
        pUiCriterion.getConditions().stream()
            .map(UiViewConverter::toScalingCriteriaCondition)
            .collect(Collectors.toList());
    return new SingleScalingCriteria(thresholdList, conditionList);
  }

  public static SingleScalingCriterionView toSingleScalingCriterionViewWithoutIntervalThreshold(
      final SingleScalingCriteria pModelCriterion) {
    // IntervalThresholdMetrics always present the latest scaling date regardless of the metric
    // request window time. For this reason it is best to ignore this in metrics and just check
    // the context document for the information.
    final List<ComputeAutoScalingThresholdView> thresholdList =
        pModelCriterion.getThresholds().stream()
            .filter(Predicate.not(ComputeAutoScalingIntervalThreshold.class::isInstance))
            .map(UiViewConverter::toComputeAutoScalingThresholdView)
            .collect(Collectors.toList());

    final List<ComputeAutoScalingConditionView> conditionList =
        pModelCriterion.getConditions().stream()
            .map(UiViewConverter::toComputeAutoScalingConditionView)
            .collect(Collectors.toList());
    return new SingleScalingCriterionView(thresholdList, conditionList);
  }

  public static SingleScalingCriterionView toSingleScalingCriterionView(
      final SingleScalingCriteria pModelCriterion) {
    final List<ComputeAutoScalingThresholdView> thresholdList =
        pModelCriterion.getThresholds().stream()
            .map(UiViewConverter::toComputeAutoScalingThresholdView)
            .collect(Collectors.toList());

    final List<ComputeAutoScalingConditionView> conditionList =
        pModelCriterion.getConditions().stream()
            .map(UiViewConverter::toComputeAutoScalingConditionView)
            .collect(Collectors.toList());
    return new SingleScalingCriterionView(thresholdList, conditionList);
  }

  public static ComputeAutoScalingThresholdView toComputeAutoScalingThresholdView(
      final ComputeAutoScalingThreshold<? extends ThresholdMetrics> pComputeAutoScalingThreshold) {
    if (pComputeAutoScalingThreshold instanceof ComputeAutoScalingCPUThreshold modelCpuThreshold) {
      return new ComputeAutoScalingCPUThresholdView(
          modelCpuThreshold.getCpuScaleWindowSeconds(),
          modelCpuThreshold.getCpuScaleAttemptWindowSeconds(),
          modelCpuThreshold.getCpuScaleThreshold(),
          ComputeAutoScalingCPUThresholdView.ThresholdTypeView.valueOf(
              modelCpuThreshold.getCpuThresholdType().name()),
          ComputeAutoScalingCPUThresholdView.MetricTypeView.valueOf(
              modelCpuThreshold.getCpuMetricType().name()));
    }
    if (pComputeAutoScalingThreshold
        instanceof ComputeAutoScalingMemoryThreshold modelMemoryThreshold) {
      return new ComputeAutoScalingMemoryThresholdView(
          modelMemoryThreshold.getMemoryScaleWindowSeconds(),
          modelMemoryThreshold.getMemoryScaleAttemptWindowSeconds(),
          modelMemoryThreshold.getMemoryScaleThreshold(),
          AutoScalingMemoryThresholdTypeView.valueOf(
              modelMemoryThreshold.getAutoScalingMemoryThresholdType().name()));
    }
    if (pComputeAutoScalingThreshold
        instanceof ComputeAutoScalingWTUsageThreshold modelWTThreshold) {
      return new ComputeAutoScalingWTUsageThresholdView(
          modelWTThreshold.getWtUsageScaleWindowSeconds(),
          modelWTThreshold.getWtUsageScaleAttemptWindowSeconds(),
          modelWTThreshold.getWtUsageThreshold());
    }
    if (pComputeAutoScalingThreshold
        instanceof ComputeAutoScalingIntervalThreshold modelIntervalThreshold) {
      return new ComputeAutoScalingIntervalThresholdView(
          modelIntervalThreshold.getClusterScaleIntervalWindowSeconds());
    }
    if (pComputeAutoScalingThreshold
        instanceof ComputeAutoScalingUptimeThreshold pUptimeThreshold) {
      return new ComputeAutoScalingUptimeThresholdView(pUptimeThreshold);
    }
    throw new IllegalStateException(
        String.format("Can not convert the threshold: %s", pComputeAutoScalingThreshold));
  }

  static ComputeAutoScalingThreshold<? extends ThresholdMetrics> toComputeAutoScalingThresholdModel(
      final ComputeAutoScalingThresholdView pComputeAutoScalingThresholdView) {
    if (pComputeAutoScalingThresholdView
        instanceof ComputeAutoScalingCPUThresholdView uiCpuThreshold) {
      return new ComputeAutoScalingCPUThreshold(
          uiCpuThreshold.getCpuScaleWindowSeconds(),
          uiCpuThreshold.getCpuScaleAttemptWindowSeconds(),
          uiCpuThreshold.getCpuScaleThreshold(),
          AutoScalingCPUThresholdType.valueOf(uiCpuThreshold.getCpuThresholdType().name()),
          AutoScalingCPUMetricType.valueOf(uiCpuThreshold.getCpuMetricType().name()));
    }
    if (pComputeAutoScalingThresholdView
        instanceof ComputeAutoScalingMemoryThresholdView uiMemoryThreshold) {
      return new ComputeAutoScalingMemoryThreshold(
          uiMemoryThreshold.getMemoryScaleWindowSeconds(),
          uiMemoryThreshold.getMemoryScaleAttemptWindowSeconds(),
          uiMemoryThreshold.getMemoryScaleThreshold(),
          AutoScalingMemoryThresholdType.valueOf(
              uiMemoryThreshold.getAutoScalingMemoryThresholdType().name()));
    }

    if (pComputeAutoScalingThresholdView
        instanceof ComputeAutoScalingIntervalThresholdView uiIntervalThreshold) {
      return new ComputeAutoScalingIntervalThreshold(
          uiIntervalThreshold.getClusterScaleIntervalWindowSeconds());
    }
    if (pComputeAutoScalingThresholdView
        instanceof ComputeAutoScalingWTUsageThresholdView uiWtThreshold) {
      return new ComputeAutoScalingWTUsageThreshold(
          uiWtThreshold.getWtUsageScaleWindowSeconds(),
          uiWtThreshold.getWtUsageScaleAttemptWindowSeconds(),
          uiWtThreshold.getWtUsageThreshold());
    }
    if (pComputeAutoScalingThresholdView
        instanceof ComputeAutoScalingUptimeThresholdView pUptimeThresholdView) {
      return new ComputeAutoScalingUptimeThreshold(pUptimeThresholdView.getMinimumUptimeSeconds());
    }
    throw new IllegalStateException(
        String.format("Can not convert the threshold: %s", pComputeAutoScalingThresholdView));
  }

  static ComputeAutoScalingConditionView toComputeAutoScalingConditionView(
      final ScalingCriteriaCondition pCondition) {
    if (pCondition instanceof InstanceSizeCondition instanceSizeCondition) {
      return new InstanceSizeConditionView(
          instanceSizeCondition.getIncludedInstanceSizeNames(),
          instanceSizeCondition.getExcludedInstanceSizeNames());
    }
    if (pCondition instanceof CloudProviderCondition cloudProviderCondition) {
      return new CloudProviderConditionView(cloudProviderCondition.getIncludedCloudProviders());
    }
    throw new IllegalStateException(String.format("Can not convert the condition: %s", pCondition));
  }

  static ScalingCriteriaCondition toScalingCriteriaCondition(
      final ComputeAutoScalingConditionView pConditionView) {
    if (pConditionView instanceof InstanceSizeConditionView instanceSizeConditionView) {
      return new InstanceSizeCondition(
          instanceSizeConditionView.getInclude(), instanceSizeConditionView.getExclude());
    }
    if (pConditionView instanceof CloudProviderConditionView cloudProviderConditionView) {
      return new CloudProviderCondition(cloudProviderConditionView.getInclude());
    }
    throw new IllegalStateException(
        String.format("Can not convert the condition view: %s", pConditionView));
  }
}
