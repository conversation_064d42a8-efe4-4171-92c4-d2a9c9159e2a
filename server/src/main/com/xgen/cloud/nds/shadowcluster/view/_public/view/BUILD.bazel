load("//server/src/main:rules.bzl", "main_library")

main_library(
    name = "view",
    srcs = glob(["**/*.java"]),
    deps = [
        "//server/src/main/com/xgen/cloud/common/mongo",
        "//server/src/main/com/xgen/cloud/nds/shadowcluster/model",
        "@maven//:com_fasterxml_jackson_core_jackson_annotations",
        "@maven//:io_swagger_core_v3_swagger_annotations_jakarta",
        "@maven//:jakarta_validation_jakarta_validation_api",
        "@maven//:org_mongodb_bson",
    ],
)
