package com.xgen.cloud.nds.datavalidation._public.model;

import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.project._public.model.CorruptionDetectionOperationOrigin;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.bson.codecs.pojo.annotations.BsonCreator;
import org.bson.codecs.pojo.annotations.BsonId;
import org.bson.codecs.pojo.annotations.BsonIgnore;
import org.bson.codecs.pojo.annotations.BsonProperty;
import org.bson.types.ObjectId;

public class DataValidationRecord {
  public static final int DEFAULT_MAX_RETRY = 3;
  public static final int DEFAULT_NUM_THREADS = 4;
  private final ObjectId _id;
  private final ObjectId _groupId;
  private final String _clusterName;
  private final String _deploymentClusterName;
  private final ObjectId _targetInstanceId;
  private final String _targetHostname;
  private final Integer _targetDiskSizeGB;
  private final String _mongoDBVersion;
  private final String _customBuildVersion;
  private final Status _status;
  private final ValidationStatus _validationStatus;
  private final Date _lastPingDate;
  private final Date _provisionStartDate;
  private final Date _completionDate;
  private final int _retryCount;
  private final InstanceHardware _instanceHardware;
  private final List<NamespaceValidationResult> _validationResults;
  private final LogMetadata _logMetadata;
  private final String _triggerReason; // JIRA ticket ID that triggered the DataValidation request
  private final String _reason; // Explanation for the current status of the DataValidationRecord
  // The number of collections to validate concurrently
  private final int _numThreads;
  private final boolean _latest;
  private final DataValidationArguments _arguments;
  private final ObjectId _planId;
  private final CorruptionDetectionOperationOrigin _operationOrigin;
  private final Boolean _resultsTruncated;
  private final boolean
      _isMigrated; // Field to be used to determine if a record has been migrated to the new

  // collections, "DataValidationCollectionResultsDao" and "DataValidationRunDao"

  @BsonCreator
  public DataValidationRecord(
      @BsonId final ObjectId pId,
      @BsonProperty(FieldDefs.GROUP_ID) final ObjectId pGroupId,
      @BsonProperty(FieldDefs.CLUSTER_NAME) final String pClusterName,
      @BsonProperty(FieldDefs.DEPLOYMENT_CLUSTER_NAME) final String pDeploymentClusterName,
      @BsonProperty(FieldDefs.TARGET_INSTANCE_ID) final ObjectId pTargetInstanceId,
      @BsonProperty(FieldDefs.TARGET_HOSTNAME) final String pTargetHostname,
      @BsonProperty(FieldDefs.TARGET_DISK_SIZE_GB) final Integer pTargetDiskSizeGB,
      @BsonProperty(FieldDefs.MONGODB_VERSION) final String pMongoDBVersion,
      @BsonProperty(FieldDefs.CUSTOM_BUILD_VERSION) final String pCustomBuildVersion,
      @BsonProperty(FieldDefs.STATUS) final Status pStatus,
      @BsonProperty(FieldDefs.VALIDATION_STATUS) final ValidationStatus pValidationStatus,
      @BsonProperty(FieldDefs.LAST_PING_DATE) final Date pLastPingDate,
      @BsonProperty(FieldDefs.PROVISION_START_DATE) final Date pProvisionStartDate,
      @BsonProperty(FieldDefs.COMPLETION_DATE) final Date pCompletionDate,
      @BsonProperty(FieldDefs.RETRY_COUNT) final int pRetryCount,
      @BsonProperty(FieldDefs.INSTANCE_HARDWARE) final InstanceHardware pHardware,
      @BsonProperty(FieldDefs.VALIDATION_RESULTS) final List<NamespaceValidationResult> pResult,
      @BsonProperty(FieldDefs.LOG_METADATA) final LogMetadata pLogMetadata,
      @BsonProperty(FieldDefs.REASON) final String pReason,
      @BsonProperty(FieldDefs.TRIGGER_REASON) final String pTriggerReason,
      @BsonProperty(FieldDefs.NUM_THREADS) final Integer pNumThreads,
      @BsonProperty(FieldDefs.LATEST) final boolean pLatest,
      @BsonProperty(FieldDefs.ARGUMENTS) final DataValidationArguments pArguments,
      @BsonProperty(FieldDefs.PLAN_ID) final ObjectId pPlanId,
      @BsonProperty(FieldDefs.OPERATION_ORIGIN)
          final CorruptionDetectionOperationOrigin pOperationOrigin,
      @BsonProperty(FieldDefs.RESULTS_TRUNCATED) final Boolean pResultsTruncated,
      @BsonProperty(FieldDefs.IS_MIGRATED) final Boolean pIsMigrated) {
    _id = pId;
    _groupId = pGroupId;
    _clusterName = pClusterName;
    _deploymentClusterName = pDeploymentClusterName;
    _targetInstanceId = pTargetInstanceId;
    _targetHostname = pTargetHostname;
    _targetDiskSizeGB = pTargetDiskSizeGB;
    _mongoDBVersion = pMongoDBVersion;
    _customBuildVersion = pCustomBuildVersion;
    _status = pStatus;
    _validationStatus = pValidationStatus;
    _lastPingDate = pLastPingDate;
    _provisionStartDate = pProvisionStartDate;
    _completionDate = pCompletionDate;
    _retryCount = pRetryCount;
    _instanceHardware = pHardware;
    _validationResults = pResult;
    _logMetadata = pLogMetadata;
    _reason = pReason;
    _triggerReason = pTriggerReason;
    _numThreads = pNumThreads != null ? pNumThreads : DEFAULT_NUM_THREADS;
    _latest = pLatest;
    _arguments = Optional.ofNullable(pArguments).orElse(DataValidationArguments.noArgs());
    _planId = pPlanId;
    _operationOrigin = pOperationOrigin;
    _resultsTruncated = pResultsTruncated != null ? pResultsTruncated : false;
    _isMigrated = pIsMigrated != null ? pIsMigrated : false;
  }

  public DataValidationRecord(
      final ObjectId pGroupId,
      final String pClusterName,
      final String pDeploymentClusterName,
      final ObjectId pTargetInstanceId,
      final CloudProvider pProvider,
      final DataValidationArguments pArguments,
      final CorruptionDetectionOperationOrigin pOperationOrigin,
      final String pTriggerReason) {
    _id = new ObjectId();
    _groupId = pGroupId;
    _clusterName = pClusterName;
    _deploymentClusterName = pDeploymentClusterName;
    _targetInstanceId = pTargetInstanceId;
    _targetHostname = null;
    _targetDiskSizeGB = null;
    _mongoDBVersion = null;
    _customBuildVersion = null;
    _status = Status.NEW;
    _validationStatus = ValidationStatus.NOT_DONE;
    _lastPingDate = null;
    _provisionStartDate = null;
    _completionDate = null;
    _retryCount = 0;
    _instanceHardware =
        InstanceHardware.getHardware(
            InstanceHardware.getEmptyHardware(pProvider, new ObjectId(), new Date(), 0));
    _validationResults = new ArrayList<>();
    _logMetadata = null;
    _reason = null;
    _triggerReason = pTriggerReason;
    _numThreads = DEFAULT_NUM_THREADS;
    _arguments = pArguments;
    _latest = true;
    _planId = null;
    _operationOrigin = pOperationOrigin;
    _resultsTruncated = false;
    _isMigrated = true; // New records are migrated by default
  }

  public enum Status {
    NEW(false),
    WORKING(false),
    COMPLETED(true),
    FAILED(true),
    NOT_NEEDED(true);

    private boolean _isTerminal;

    Status(final boolean pIsTerminal) {
      this._isTerminal = pIsTerminal;
    }

    public boolean isTerminal() {
      return this._isTerminal;
    }

    public static Optional<Status> fromName(final String pName) {
      if (Arrays.stream(values()).map(Enum::name).anyMatch(v -> v.equals(pName))) {
        return Optional.of(valueOf(pName));
      }
      return Optional.empty();
    }
  }

  public enum ValidationStatus {
    NOT_DONE,
    SUCCESS,
    FAILED,
    INCONCLUSIVE;

    public static Optional<ValidationStatus> fromName(final String pName) {
      if (Arrays.stream(values()).map(Enum::name).anyMatch(v -> v.equals(pName))) {
        return Optional.of(valueOf(pName));
      }
      return Optional.empty();
    }
  }

  // Workaround for MongoDB Java Driver POJO garbage handling of Optionals
  @BsonIgnore
  public Optional<Date> getOptionalLastPingDate() {
    return Optional.ofNullable(_lastPingDate);
  }

  public Date getLastPingDate() {
    return _lastPingDate;
  }

  @BsonIgnore
  public Optional<Date> getOptionalProvisionStartDate() {
    return Optional.ofNullable(_provisionStartDate);
  }

  public Date getProvisionStartDate() {
    return _provisionStartDate;
  }

  @BsonIgnore
  public Optional<Date> getOptionalCompletionDate() {
    return Optional.ofNullable(_completionDate);
  }

  public Date getCompletionDate() {
    return _completionDate;
  }

  @BsonIgnore
  public Optional<Date> getOptionalValidationStartDate() {
    if (_logMetadata == null) {
      return Optional.empty();
    }
    return Optional.ofNullable(_logMetadata.getValidationStartDate());
  }

  @BsonIgnore
  public Optional<Date> getOptionalValidationEndDate() {
    if (_logMetadata == null) {
      return Optional.empty();
    }
    return Optional.ofNullable(_logMetadata.getValidationEndDate());
  }

  @BsonIgnore
  public Optional<LogMetadata> getOptionalLogMetadata() {
    return Optional.ofNullable(_logMetadata);
  }

  @BsonIgnore
  public Optional<String> getOptionalReason() {
    return Optional.ofNullable(_reason);
  }

  public String getReason() {
    return _reason;
  }

  @BsonIgnore
  public Optional<String> getOptionalTriggerReason() {
    return Optional.ofNullable(_triggerReason);
  }

  public String getTriggerReason() {
    return _triggerReason;
  }

  public int getNumThreads() {
    return _numThreads;
  }

  public DataValidationArguments getArguments() {
    return _arguments;
  }

  public boolean getLatest() {
    return _latest;
  }

  @BsonIgnore
  public Optional<ObjectId> getOptionalPlanId() {
    return Optional.ofNullable(_planId);
  }

  public ObjectId getPlanId() {
    return _planId;
  }

  public CorruptionDetectionOperationOrigin getOperationOrigin() {
    return _operationOrigin != null ? _operationOrigin : CorruptionDetectionOperationOrigin.MANUAL;
  }

  public Boolean getResultsTruncated() {
    return _resultsTruncated;
  }

  public boolean shouldKeepHardwareAfterCompletion() {
    return Optional.ofNullable(_arguments.getKeepHardwareAfterCompletion()).orElse(false);
  }

  public boolean getIsMigrated() {
    return _isMigrated;
  }

  public static class FieldDefs {
    public static final String ID = "_id";
    public static final String GROUP_ID = "groupId";
    public static final String CLUSTER_NAME = "clusterName";
    public static final String DEPLOYMENT_CLUSTER_NAME = "deploymentClusterName";
    public static final String TARGET_INSTANCE_ID = "targetInstanceId";
    public static final String MONGODB_VERSION = "mongoDBVersion";
    public static final String CUSTOM_BUILD_VERSION = "customBuildVersion";
    public static final String TARGET_HOSTNAME = "targetHostname";
    public static final String TARGET_DISK_SIZE_GB = "targetDiskSizeGB";

    public static final String STATUS = "status";
    public static final String VALIDATION_STATUS = "validationStatus";
    public static final String LAST_PING_DATE = "lastPingDate";
    public static final String PROVISION_START_DATE = "provisionStartDate";
    public static final String COMPLETION_DATE = "completionDate";

    public static final String RETRY_COUNT = "retryCount";
    public static final String INSTANCE_HARDWARE = "instanceHardware";
    public static final String VALIDATION_RESULTS = "validationResults";
    public static final String LOG_METADATA = "logMetadata";
    public static final String TRIGGER_REASON = "triggerReason";
    public static final String REASON = "reason";
    public static final String NUM_THREADS = "numThreads";
    public static final String LATEST = "latest";
    public static final String ARGUMENTS = "arguments";
    public static final String PLAN_ID = "planId";
    public static final String OPERATION_ORIGIN = "operationOrigin";
    public static final String RESULTS_TRUNCATED = "resultsTruncated";
    public static final String IS_MIGRATED = "isMigrated";
  }

  public static class DataValidationRecordBuilder {
    private ObjectId id;

    private ObjectId groupId;

    private String clusterName;

    private String deploymentClusterName;

    private ObjectId targetInstanceId;

    private String targetHostname;

    private Integer targetDiskSizeGB;

    private String mongoDBVersion;

    private String customBuildVersion;

    private Status status;

    private ValidationStatus validationStatus;

    private Date lastPingDate;

    private Date provisionStartDate;

    private Date completionDate;

    private int retryCount;

    private InstanceHardware instanceHardware;

    private List<NamespaceValidationResult> validationResults;

    private LogMetadata logMetadata;

    private String triggerReason;

    private String reason;

    private int numThreads;

    private boolean latest;

    private DataValidationArguments arguments;

    private ObjectId planId;

    private CorruptionDetectionOperationOrigin operationOrigin;

    private Boolean resultsTruncated;

    private boolean isMigrated;

    DataValidationRecordBuilder() {}

    public DataValidationRecord.DataValidationRecordBuilder id(final ObjectId id) {
      this.id = id;
      return this;
    }

    public DataValidationRecord.DataValidationRecordBuilder groupId(final ObjectId groupId) {
      this.groupId = groupId;
      return this;
    }

    public DataValidationRecord.DataValidationRecordBuilder clusterName(final String clusterName) {
      this.clusterName = clusterName;
      return this;
    }

    public DataValidationRecord.DataValidationRecordBuilder deploymentClusterName(
        final String deploymentClusterName) {
      this.deploymentClusterName = deploymentClusterName;
      return this;
    }

    public DataValidationRecord.DataValidationRecordBuilder targetInstanceId(
        final ObjectId targetInstanceId) {
      this.targetInstanceId = targetInstanceId;
      return this;
    }

    public DataValidationRecord.DataValidationRecordBuilder targetHostname(
        final String targetHostname) {
      this.targetHostname = targetHostname;
      return this;
    }

    public DataValidationRecord.DataValidationRecordBuilder targetDiskSizeGB(
        final Integer targetDiskSizeGB) {
      this.targetDiskSizeGB = targetDiskSizeGB;
      return this;
    }

    public DataValidationRecord.DataValidationRecordBuilder mongoDBVersion(
        final String mongoDBVersion) {
      this.mongoDBVersion = mongoDBVersion;
      return this;
    }

    public DataValidationRecord.DataValidationRecordBuilder customBuildVersion(
        final String customBuildVersion) {
      this.customBuildVersion = customBuildVersion;
      return this;
    }

    public DataValidationRecord.DataValidationRecordBuilder status(final Status status) {
      this.status = status;
      return this;
    }

    public DataValidationRecord.DataValidationRecordBuilder validationStatus(
        final ValidationStatus validationStatus) {
      this.validationStatus = validationStatus;
      return this;
    }

    public DataValidationRecord.DataValidationRecordBuilder lastPingDate(final Date lastPingDate) {
      this.lastPingDate = lastPingDate;
      return this;
    }

    public DataValidationRecord.DataValidationRecordBuilder provisionStartDate(
        final Date provisionStartDate) {
      this.provisionStartDate = provisionStartDate;
      return this;
    }

    public DataValidationRecord.DataValidationRecordBuilder completionDate(
        final Date completionDate) {
      this.completionDate = completionDate;
      return this;
    }

    public DataValidationRecord.DataValidationRecordBuilder retryCount(final int retryCount) {
      this.retryCount = retryCount;
      return this;
    }

    public DataValidationRecord.DataValidationRecordBuilder instanceHardware(
        final InstanceHardware instanceHardware) {
      this.instanceHardware = instanceHardware;
      return this;
    }

    public DataValidationRecord.DataValidationRecordBuilder validationResults(
        final List<NamespaceValidationResult> validationResults) {
      this.validationResults = validationResults;
      return this;
    }

    public DataValidationRecord.DataValidationRecordBuilder logMetadata(
        final LogMetadata logMetadata) {
      this.logMetadata = logMetadata;
      return this;
    }

    public DataValidationRecord.DataValidationRecordBuilder reason(final String reason) {
      this.reason = reason;
      return this;
    }

    public DataValidationRecord.DataValidationRecordBuilder triggerReason(
        final String triggerReason) {
      this.triggerReason = triggerReason;
      return this;
    }

    public DataValidationRecord.DataValidationRecordBuilder numThreads(final int numThreads) {
      this.numThreads = numThreads;
      return this;
    }

    public DataValidationRecord.DataValidationRecordBuilder arguments(
        final DataValidationArguments pArguments) {
      this.arguments = pArguments;
      return this;
    }

    public DataValidationRecord.DataValidationRecordBuilder latest(final boolean latest) {
      this.latest = latest;
      return this;
    }

    public DataValidationRecord.DataValidationRecordBuilder planId(final ObjectId planId) {
      this.planId = planId;
      return this;
    }

    public DataValidationRecord.DataValidationRecordBuilder operationOrigin(
        final CorruptionDetectionOperationOrigin operationOrigin) {
      this.operationOrigin = operationOrigin;
      return this;
    }

    public DataValidationRecord.DataValidationRecordBuilder resultsTruncated(
        final Boolean resultsTruncated) {
      this.resultsTruncated = resultsTruncated;
      return this;
    }

    public DataValidationRecord.DataValidationRecordBuilder isMigrated(final Boolean isMigrated) {
      this.isMigrated = isMigrated;
      return this;
    }

    public DataValidationRecord build() {
      return new DataValidationRecord(
          this.id,
          this.groupId,
          this.clusterName,
          this.deploymentClusterName,
          this.targetInstanceId,
          this.targetHostname,
          this.targetDiskSizeGB,
          this.mongoDBVersion,
          this.customBuildVersion,
          this.status,
          this.validationStatus,
          this.lastPingDate,
          this.provisionStartDate,
          this.completionDate,
          this.retryCount,
          this.instanceHardware,
          this.validationResults,
          this.logMetadata,
          this.reason,
          this.triggerReason,
          this.numThreads,
          this.latest,
          this.arguments,
          this.planId,
          this.operationOrigin,
          this.resultsTruncated,
          this.isMigrated);
    }

    @Override
    public String toString() {
      return new ToStringBuilder(this)
          .append(id)
          .append(groupId)
          .append(clusterName)
          .append(deploymentClusterName)
          .append(targetInstanceId)
          .append(targetHostname)
          .append(targetDiskSizeGB)
          .append(mongoDBVersion)
          .append(customBuildVersion)
          .append(status)
          .append(validationStatus)
          .append(lastPingDate)
          .append(provisionStartDate)
          .append(completionDate)
          .append(retryCount)
          .append(instanceHardware)
          .append(validationResults)
          .append(logMetadata)
          .append(reason)
          .append(triggerReason)
          .append(numThreads)
          .append(latest)
          .append(arguments)
          .append(planId)
          .append(operationOrigin)
          .append(resultsTruncated)
          .append(isMigrated)
          .toString();
    }
  }

  public static DataValidationRecord.DataValidationRecordBuilder builder() {
    return new DataValidationRecord.DataValidationRecordBuilder();
  }

  public DataValidationRecord.DataValidationRecordBuilder toBuilder() {
    return new DataValidationRecordBuilder()
        .id(this._id)
        .groupId(this._groupId)
        .clusterName(this._clusterName)
        .deploymentClusterName(this._deploymentClusterName)
        .targetInstanceId(this._targetInstanceId)
        .targetHostname(this._targetHostname)
        .targetDiskSizeGB(this._targetDiskSizeGB)
        .mongoDBVersion(this._mongoDBVersion)
        .customBuildVersion(this._customBuildVersion)
        .status(this._status)
        .validationStatus(this._validationStatus)
        .lastPingDate(this._lastPingDate)
        .provisionStartDate(this._provisionStartDate)
        .completionDate(this._completionDate)
        .retryCount(this._retryCount)
        .instanceHardware(this._instanceHardware)
        .validationResults(this._validationResults)
        .logMetadata(this._logMetadata)
        .reason(this._reason)
        .triggerReason(this._triggerReason)
        .numThreads(this._numThreads)
        .latest(this._latest)
        .arguments(this._arguments)
        .planId(this._planId)
        .operationOrigin(this._operationOrigin)
        .resultsTruncated(this._resultsTruncated)
        .isMigrated(this._isMigrated);
  }

  public ObjectId getId() {
    return this._id;
  }

  public ObjectId getGroupId() {
    return this._groupId;
  }

  public String getClusterName() {
    return this._clusterName;
  }

  public String getDeploymentClusterName() {
    return this._deploymentClusterName;
  }

  public ObjectId getTargetInstanceId() {
    return this._targetInstanceId;
  }

  @BsonIgnore
  public Optional<String> getOptionalTargetHostname() {
    return Optional.ofNullable(this._targetHostname);
  }

  public String getTargetHostname() {
    return this._targetHostname;
  }

  @BsonIgnore
  public Optional<Integer> getOptionalTargetDiskSizeGB() {
    return Optional.ofNullable(this._targetDiskSizeGB);
  }

  public Integer getTargetDiskSizeGB() {
    return this._targetDiskSizeGB;
  }

  @BsonIgnore
  public Optional<String> getOptionalMongoDBVersion() {
    return Optional.ofNullable(this._mongoDBVersion);
  }

  public String getMongoDBVersion() {
    return this._mongoDBVersion;
  }

  @BsonIgnore
  public Optional<String> getOptionalCustomBuildVersion() {
    return Optional.ofNullable(this._customBuildVersion);
  }

  public String getCustomBuildVersion() {
    return this._customBuildVersion;
  }

  public Status getStatus() {
    return this._status;
  }

  public ValidationStatus getValidationStatus() {
    return this._validationStatus;
  }

  public int getRetryCount() {
    return this._retryCount;
  }

  public InstanceHardware getInstanceHardware() {
    return this._instanceHardware;
  }

  public List<NamespaceValidationResult> getValidationResults() {
    return this._validationResults;
  }
}
