package com.xgen.cloud.alerts.context._public.svc;

import com.mongodb.DBObject;
import com.xgen.cloud.alerts.context._public.model.GroupProcessingContext;
import com.xgen.cloud.atm.core._public.model.AgentAudit;
import com.xgen.svc.mms.model.agent.AgentStatus;
import java.util.List;
import java.util.Set;

public interface AgentProcessingContext extends GroupProcessingContext {
  AgentStatus getMonitoringAgentStatus(final long pMinDowntimeMsec);

  AgentStatus getBackupAgentStatus(final String pTag, final long pMinDowntimeMsec);

  Set<String> getBackupAgentTags();

  DBObject getBackupAgentSession(final String pTag);

  List<AgentAudit> getAutomationAgentAudits();
}
