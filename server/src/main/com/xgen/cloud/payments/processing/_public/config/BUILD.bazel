load("//server/src/main:rules.bzl", "main_library")

main_library(
    name = "config",
    srcs = glob(["**/*.java"]),
    deps = [
        "//server/src/main/com/xgen/cloud/payments/common",
        "//server/src/main/com/xgen/svc/mms/model/billing",
        "@maven//:com_xgen_devtools_configservicesdk",
        "@maven//:jakarta_inject_jakarta_inject_api",
        "@maven//:org_apache_commons_commons_configuration2",
    ],
)
