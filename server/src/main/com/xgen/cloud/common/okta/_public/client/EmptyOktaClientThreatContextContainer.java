package com.xgen.cloud.common.okta._public.client;

import jakarta.inject.Singleton;
import java.util.Optional;

/**
 * This is essentially a no-op {@link OktaClientThreatContextContainer} for use when there isn't a
 * way to get the context from a request.
 */
@Singleton
public class EmptyOktaClientThreatContextContainer implements OktaClientThreatContextContainer {

  public EmptyOktaClientThreatContextContainer() {}

  /**
   * Gets an empty {@link OktaClientThreatContext}.
   *
   * @return the empty optional.
   */
  @Override
  public Optional<OktaClientThreatContext> getContext() {
    return Optional.empty();
  }
}
