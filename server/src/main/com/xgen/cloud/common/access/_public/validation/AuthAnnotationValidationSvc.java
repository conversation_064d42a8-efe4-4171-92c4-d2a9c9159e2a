package com.xgen.cloud.common.access._public.validation;

import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.SvcException;
import jakarta.inject.Singleton;
import java.util.HashSet;
import java.util.Set;
import java.util.regex.Pattern;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Service for validating Auth annotation fields, particularly EPA and CFA values.
 * Ensures EPA follows naming conventions and CFA values are unique per endpoint.
 */
@Singleton
public class AuthAnnotationValidationSvc {
  private static final Logger LOG = LoggerFactory.getLogger(AuthAnnotationValidationSvc.class);

  // EPA must start with "epa." namespace
  private static final String EPA_NAMESPACE_PREFIX = "epa.";
  
  // EPA pattern: epa.{resourceType}.{className}.{methodName}.{httpMethod}
  private static final Pattern EPA_PATTERN = Pattern.compile(
      "^epa\\.(organization|project|cluster|global)\\.[A-Za-z0-9_]+\\.[A-Za-z0-9_]+\\.(GET|POST|PUT|PATCH|DELETE|HEAD|OPTIONS)$");
  
  // CFA should follow a hierarchical naming pattern (e.g., "projects.clusters.read")
  private static final Pattern CFA_PATTERN = Pattern.compile(
      "^[a-z][a-z0-9]*(?:\\.[a-z][a-z0-9]*)*\\.[a-z][a-z0-9]*$");
  
  // Valid resource types
  private static final Set<String> VALID_RESOURCE_TYPES = Set.of(
      "organization", "project", "cluster", "global");
  
  // Track used CFA values to ensure uniqueness (in production, this would be persisted)
  private final Set<String> usedCfaValues = new HashSet<>();

  /**
   * Validates an Auth annotation for EPA/CFA compliance.
   *
   * @param auth the Auth annotation to validate
   * @param className the class name where the annotation is used
   * @param methodName the method name where the annotation is used
   * @param isMmsEndpoint whether this is an MMS endpoint (requires EPA)
   * @throws SvcException if validation fails
   */
  public void validateAuthAnnotation(
      final Auth auth,
      final String className,
      final String methodName,
      final boolean isMmsEndpoint) throws SvcException {

    // For MMS endpoints, EPA is mandatory
    if (isMmsEndpoint) {
      if (StringUtils.isEmpty(auth.endpointAction())) {
        throw new SvcException(
            CommonErrorCode.INVALID_ARGUMENT,
            String.format("MMS endpoint %s.%s must have endpointAction specified",
                className, methodName));
      }

      if (StringUtils.isEmpty(auth.resourceType())) {
        throw new SvcException(
            CommonErrorCode.INVALID_ARGUMENT,
            String.format("MMS endpoint %s.%s must have resourceType specified",
                className, methodName));
      }
    }

    // If EPA is specified, validate it
    if (!StringUtils.isEmpty(auth.endpointAction())) {
      validateEndpointAction(auth.endpointAction(), className, methodName);
    }

    // If CFA is specified, validate it (optional for sample endpoints)
    if (!StringUtils.isEmpty(auth.customerFacingAction())) {
      validateCustomerFacingAction(auth.customerFacingAction());
    }

    // Validate resource type if specified
    if (!StringUtils.isEmpty(auth.resourceType())) {
      validateResourceType(auth.resourceType());
    }
  }

  /**
   * Overloaded method for backward compatibility - assumes non-MMS endpoint.
   */
  public void validateAuthAnnotation(
      final Auth auth,
      final String className,
      final String methodName) throws SvcException {
    validateAuthAnnotation(auth, className, methodName, false);
  }

  /**
   * Validates EPA (Endpoint Action) format and namespace.
   */
  private void validateEndpointAction(
      final String endpointAction, 
      final String className, 
      final String methodName) throws SvcException {
    
    if (!endpointAction.startsWith(EPA_NAMESPACE_PREFIX)) {
      throw new SvcException(
          CommonErrorCode.INVALID_ARGUMENT,
          String.format("Endpoint action '%s' must start with '%s' namespace", 
              endpointAction, EPA_NAMESPACE_PREFIX));
    }
    
    if (!EPA_PATTERN.matcher(endpointAction).matches()) {
      throw new SvcException(
          CommonErrorCode.INVALID_ARGUMENT,
          String.format("Endpoint action '%s' does not follow required pattern: %s", 
              endpointAction, "epa.{resourceType}.{className}.{methodName}.{httpMethod}"));
    }
    
    LOG.debug("Validated EPA: {} for {}.{}", endpointAction, className, methodName);
  }

  /**
   * Validates CFA (Customer Facing Action) format and uniqueness.
   */
  private void validateCustomerFacingAction(final String customerFacingAction) throws SvcException {
    if (!CFA_PATTERN.matcher(customerFacingAction).matches()) {
      throw new SvcException(
          CommonErrorCode.INVALID_ARGUMENT,
          String.format("Customer facing action '%s' does not follow required pattern. " +
              "Use lowercase dot-separated format like 'projects.clusters.read'", 
              customerFacingAction));
    }
    
    // Check for uniqueness (in production, this would check against a database)
    if (usedCfaValues.contains(customerFacingAction)) {
      throw new SvcException(
          CommonErrorCode.INVALID_ARGUMENT,
          String.format("Customer facing action '%s' is already in use. " +
              "CFA values must be unique per endpoint", customerFacingAction));
    }
    
    usedCfaValues.add(customerFacingAction);
    LOG.debug("Validated CFA: {}", customerFacingAction);
  }

  /**
   * Validates resource type value.
   */
  private void validateResourceType(final String resourceType) throws SvcException {
    if (!VALID_RESOURCE_TYPES.contains(resourceType.toLowerCase())) {
      throw new SvcException(
          CommonErrorCode.INVALID_ARGUMENT,
          String.format("Invalid resourceType '%s'. Valid values are: %s", 
              resourceType, VALID_RESOURCE_TYPES));
    }
  }

  /**
   * Generates EPA name for a given class, method, and HTTP method.
   * This is used by auto-generation tools.
   */
  public String generateEndpointAction(
      final String resourceType,
      final String className, 
      final String methodName, 
      final String httpMethod) {
    return String.format("%s%s.%s.%s.%s", 
        EPA_NAMESPACE_PREFIX, resourceType.toLowerCase(), className, methodName, httpMethod);
  }

  /**
   * Suggests CFA name based on resource hierarchy and operation.
   * This is a helper for developers to generate appropriate CFA names.
   */
  public String suggestCustomerFacingAction(
      final String resourceType, 
      final String operation) {
    return String.format("%ss.%s", resourceType.toLowerCase(), operation.toLowerCase());
  }

  /**
   * Clears the used CFA values cache. Used for testing.
   */
  public void clearUsedCfaValues() {
    usedCfaValues.clear();
  }
}
