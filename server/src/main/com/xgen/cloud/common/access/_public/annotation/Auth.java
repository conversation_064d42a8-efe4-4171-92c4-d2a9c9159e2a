package com.xgen.cloud.common.access._public.annotation;

import com.xgen.cloud.common.constants._public.model.actions.ActionConstants;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * This is the annotation that we use for endpoints that require authorization with the AuthZ
 * Service. Using this annotation on a class is currently only supported for @ServerEndpoint
 * classes. Support for Jersey / gRPC may be added in the future.
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE, ElementType.METHOD})
public @interface Auth {
  /** Where the Auth annotation is enforced */
  enum Enforcement {
    // In middleware such a Filter (e.g. AuthzFilter) or gRPC interceptor
    FILTER_OR_INTERCEPTOR,

    // Manually in the resource method. The annotation will still be included for testing.
    INLINE,

    // Relying on an org member check in the filter
    ORG_MEMBERSHIP,

    // Not enforced
    OFF
  }

  /** Determines where this Auth annotation is enforced */
  Enforcement enforce() default Enforcement.FILTER_OR_INTERCEPTOR;

  /**
   * The action required to call the endpoint, e.g. ActionConstants.IAM_USERS_READ. If
   * `serviceAction` is defined as well, this will be used specifically for initiator-based auth.
   *
   * @deprecated Use endpointAction and customerFacingAction for MMS endpoints.
   * This field is still used for service-to-service authorization.
   */
  ActionConstants action() default ActionConstants.NONE;

  /** Determines if this annotation will be included for testing */
  boolean runTest() default true;

  /** The service identifier. Currently, only "cloud" is used */
  String service() default "cloud"; // can be removed
  // MMS EPA/CFA fields - use for MMS endpoints with string-based actions
  /**
   * Endpoint Action (EPA) - auto-generated internal action name for MMS endpoints.
   * Should follow pattern: epa.{resourceType}.{className}.{methodName}.{httpMethod}
   *
   * REQUIRED for all MMS endpoints. Auto-generated by build tools.
   * Only used for MMS authorization, not service-to-service.
   */
  String endpointAction() default "";

  /**
   * Customer Facing Action (CFA) - action name exposed to customers.
   * Should be unique per endpoint. Used for MMS authorization.
   *
   * OPTIONAL - only specified for sample endpoints as proof-of-concept.
   * When not specified, EPA is used for both internal and customer-facing authorization.
   */
  String customerFacingAction() default "";

  /**
   * Resource type for this endpoint (e.g., "project", "organization", "cluster", "global").
   * Used to determine the primary resource type for authorization.
   *
   * REQUIRED when endpointAction is specified.
   */
  String resourceType() default "";





  // S2S specific fields - only use for S2S authorization (aka no monolith)
  /** Is initiator-based authentication (authN) required for this endpoint, defaults to true */
  // Initiator is the entity that initiates the request, for example a user in the browser
  boolean initiatorAuthentication() default true;

  /** Is service-based authentication (authN) required for this endpoint, defaults to false */
  // Service is the service making a request
  boolean serviceAuthentication() default false;

  /** Is initiator-based authorization (authZ) required for this endpoint, defaults to true */
  boolean initiatorAuthorization() default true;

  /** Is service-based authorization (authZ) required for this endpoint, defaults to false */
  boolean serviceAuthorization() default false;

  /**
   * The action required to call the endpoint by a service. If left blank `action` is used for both
   * service and initiator action
   */
  ActionConstants serviceAction() default ActionConstants.NONE;
}
