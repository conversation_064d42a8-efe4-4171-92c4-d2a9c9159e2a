package com.xgen.cloud.common.authz._public.model;

import com.xgen.cloud.common.constants._public.model.actions.ActionConstants;
import com.xgen.cloud.common.constants._public.model.resources.ResourceTypeConstants;
import java.util.Map;

/**
 * Encapsulates an individually-verifiable authz request. Differs from something like
 * AuthzRequestInfo in that it holds a mapping of ResourceType to resource id which is necessary to
 * perform RBAC authz checks
 *
 * @param action action to verify against
 * @param resourceTypeToIds map of ResourceType to resource id to verify against
 */
public record AuthzRequirement(
    ActionConstants action, Map<ResourceTypeConstants, String> resourceTypeToIds) {

  /**
   * Creates an AuthzRequirement for EPA (Endpoint Action) strings.
   * This is used for MMS endpoints that use string-based actions instead of ActionConstants.
   *
   * @param endpointAction EPA string (e.g., "epa.project.ExampleResource.method.GET")
   * @param primaryResourceType the primary resource type for this action
   * @param resourceTypeToIds map of ResourceType to resource id to verify against
   * @return AuthzRequirement that can be used with EPA-aware authorization services
   */
  public static AuthzRequirement forEndpointAction(
      String endpointAction,
      ResourceTypeConstants primaryResourceType,
      Map<ResourceTypeConstants, String> resourceTypeToIds) {
    // Create a synthetic ActionConstants for EPA that contains the necessary metadata
    // This allows existing authorization code to work with EPA strings
    return new EpaAuthzRequirement(endpointAction, primaryResourceType, resourceTypeToIds);
  }

  /**
   * Extended AuthzRequirement that supports EPA strings while maintaining compatibility
   * with existing authorization infrastructure.
   */
  public static final class EpaAuthzRequirement extends AuthzRequirement {
    private final String endpointAction;
    private final ResourceTypeConstants primaryResourceType;

    private EpaAuthzRequirement(
        String endpointAction,
        ResourceTypeConstants primaryResourceType,
        Map<ResourceTypeConstants, String> resourceTypeToIds) {
      // Use NONE as placeholder since we'll override the action methods
      super(ActionConstants.NONE, resourceTypeToIds);
      this.endpointAction = endpointAction;
      this.primaryResourceType = primaryResourceType;
    }

    /**
     * Returns the EPA string instead of the ActionConstants action.
     */
    public String getEndpointAction() {
      return endpointAction;
    }

    /**
     * Returns the primary resource type for this EPA.
     */
    public ResourceTypeConstants getPrimaryResourceType() {
      return primaryResourceType;
    }

    /**
     * Indicates this is an EPA-based requirement.
     */
    public boolean isEndpointAction() {
      return true;
    }
  }

  /**
   * Indicates whether this requirement uses an EPA string (true) or ActionConstants (false).
   */
  public boolean isEndpointAction() {
    return false;
  }
}
