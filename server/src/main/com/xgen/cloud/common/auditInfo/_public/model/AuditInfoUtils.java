package com.xgen.cloud.common.auditInfo._public.model;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xgen.cloud.common.jackson._public.CustomJacksonJsonProvider;

public class AuditInfoUtils {
  public static String serialize(final AuditInfo pAuditInfo) throws Exception {
    final ObjectMapper objectMapper = CustomJacksonJsonProvider.createObjectMapper();
    return objectMapper.writeValueAsString(pAuditInfo);
  }

  public static AuditInfo deserialize(final String pAuditInfo) throws Exception {
    final ObjectMapper objectMapper = CustomJacksonJsonProvider.createObjectMapper();
    objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    if (pAuditInfo != null && !pAuditInfo.isEmpty()) {
      return objectMapper.readValue(pAuditInfo, AuditInfo.class);
    }
    return null;
  }
}
