package com.xgen.cloud.account._public.svc;

import com.xgen.cloud.common.model._public.error.ErrorCode;
import jakarta.annotation.Nullable;

public enum DeviceCodeErrorCode implements ErrorCode {
  DEVICE_CODE_EXPIRED("The device code has expired"),
  DEVICE_CODE_INVALID_STATE("The device code can't be verified");

  DeviceCodeErrorCode(final String pMessage) {
    this(pMessage, null);
  }

  DeviceCodeErrorCode(final String pMessage, @Nullable final String pMessageFormat) {
    _message = pMessage;
    _messageFormat = pMessageFormat;
  }

  private final String _message;
  @Nullable private final String _messageFormat;

  @Override
  public String getMessage() {
    return _message;
  }

  @Override
  @Nullable
  public String getMessageFormat() {
    return _messageFormat;
  }
}
