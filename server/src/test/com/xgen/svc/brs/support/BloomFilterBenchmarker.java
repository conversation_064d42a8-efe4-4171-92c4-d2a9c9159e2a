package com.xgen.svc.brs.support;

import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoCursor;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.MongoIterable;
import com.xgen.cloud.brs.core._public.model.StringBloomFilter;
import com.xgen.cloud.common.driverwrappers._public.legacy.MongoClient;
import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class BloomFilterBenchmarker {
  public static final String MONGO_HOST = "localhost";
  public static final int MONGO_PORT = 27017;

  public static final int KiB = 1024;
  public static final int MiB = 1024 * KiB;
  public static final int NUM_BITS = 58 * MiB;
  public static final int NUM_BLOOM_HASHES = 5;
  public static final int NUM_IN_FILTER_ELEMENTS = 10_000_000;
  public static final int NUM_NOT_IN_FILTER_ELEMENTS = 10_000_000;
  public static final int IN_FILTER_ELEMENT_LENGTH = 40;
  public static final int NOT_IN_FILTER_ELEMENT_LENGTH = 40;

  private static final Logger LOG = LoggerFactory.getLogger(BloomFilterBenchmarker.class);
  private static final Stopwatch STOPWATCH = new Stopwatch();
  private static final Generator GENERATOR = new Generator();

  private static AtomicInteger hits;

  /**
   * This benchmarker is currently configured to store all the data in memory before starting the
   * worker threads that test the bloom filter, but can be configured to load the data from disk at
   * the same time as running the worker threads that test the bloom filter if we're running out of
   * memory.
   *
   * <p>In addition, all the code that depends on the bloom filter library has been commented out in
   * so that the library can be removed as a dependency.
   */
  public static void main(String[] jargs) throws Exception {
    MongoClient mongoClient = new MongoClient(MONGO_HOST, MONGO_PORT);

    try {
      MongoDatabase database = mongoClient.getDatabase("test");
      MongoCollection<Document> inFilterElements = database.getCollection("inFilterElements");
      MongoCollection<Document> notInFilterElements = database.getCollection("notInFilterElements");

      // Comment this line out if you have existing data in the collections.
      generateData(inFilterElements, notInFilterElements);
      /*
      testBloomFilter1(inFilterElements, notInFilterElements);
      */
      testBloomFilter2(inFilterElements, notInFilterElements);

    } finally {
      mongoClient.close();
    }
  }

  private static void generateData(
      MongoCollection<Document> inFilterElements, MongoCollection<Document> notInFilterElements) {
    LOG.debug("------------------------------------------------");
    LOG.debug("- Preparing test data.");
    LOG.debug("------------------------------------------------");

    LOG.debug("[In Filter Elements] Start.");
    STOPWATCH.restart();
    fillWithElements(inFilterElements, NUM_IN_FILTER_ELEMENTS, IN_FILTER_ELEMENT_LENGTH);
    STOPWATCH.lookAtTimeInMs();
    long timeTakenMs = STOPWATCH.lookAtTimeInMs();
    LOG.debug("[In Filter Elements] Finished in {}ms.", timeTakenMs);

    LOG.debug("[Not In Filter Elements] Start.");
    STOPWATCH.restart();
    fillWithElements(notInFilterElements, NUM_NOT_IN_FILTER_ELEMENTS, NOT_IN_FILTER_ELEMENT_LENGTH);
    STOPWATCH.lookAtTimeInMs();
    timeTakenMs = STOPWATCH.lookAtTimeInMs();
    LOG.debug("[Not In Filter Elements] Finished in {}ms.", timeTakenMs);
  }

  private static void fillWithElements(
      MongoCollection<Document> collection, int numElements, int elementLength) {
    collection.drop();

    final int ELEMENTS_PER_DOC = 100_000;
    final int DOCS_PER_BATCH = 10;
    List<Document> batch = new ArrayList<>(DOCS_PER_BATCH);
    int elementNum = 0;
    while (elementNum < numElements) {
      batch.clear();
      while (elementNum < numElements && batch.size() < DOCS_PER_BATCH) {
        int elementsToAdd = ELEMENTS_PER_DOC;
        if (elementNum + elementsToAdd > numElements) {
          elementsToAdd = numElements - elementNum;
        }

        batch.add(new Document("elements", GENERATOR.nextStrings(elementsToAdd, elementLength)));
        elementNum += elementsToAdd;
      }
      collection.insertMany(batch);
      LOG.debug("Inserted {} elements.", elementNum);
    }
  }

  /*
  private static void testBloomFilter1(MongoCollection<Document> inFilterElements,
                                       MongoCollection<Document> notInFilterElements) throws Exception {
      LOG.debug("------------------------------------------------");
      LOG.debug("- Beginning tests for bloom filter 1.");
      LOG.debug("------------------------------------------------");

      LOG.debug("[Initialization] Start.");
      STOPWATCH.restart();
      BloomFilter<String> filter = new BloomFilter<>(NUM_BITS, NUM_BLOOM_HASHES, true);
      BlockingQueue<String> workQueue = new ArrayBlockingQueue<>(10_000_000);
      List<TesterThread> workerThreads = new ArrayList<>();
      for (int workNum = 0; workNum < 4; ++workNum) {
          TesterThread workerThread = TesterThread.getFilter1AddThread(workQueue, filter);
          workerThreads.add(workerThread);
      }
      LOG.debug("[Initialization] Finished in {}ms.", STOPWATCH.lookAtTimeInMs());

      LOG.debug("[Filter Population] Start.");
      STOPWATCH.restart();
      populateWorkQueue(inFilterElements, workQueue, workerThreads);
      LOG.debug("Done queuing in {}ms.", STOPWATCH.lookAtTimeInMs());
      startWorkerThreads(workerThreads);
      completeWorkerThreads(workerThreads);
      LOG.debug("[Filter Population] Finished in {}ms.", STOPWATCH.lookAtTimeInMs());

      LOG.debug("[Initialization 2] Start.");
      STOPWATCH.restart();
      workerThreads.clear();
      for (int workNum = 0; workNum < 10; ++workNum) {
          TesterThread workerThread = TesterThread.getFilter1VerifyThread(workQueue, filter);
          workerThreads.add(workerThread);
      }
      LOG.debug("[Initialization 2] Finished in {}ms.", STOPWATCH.lookAtTimeInMs());

      LOG.debug("[Filter Verification] Start.");
      STOPWATCH.restart();
      populateWorkQueue(inFilterElements, workQueue, workerThreads);
      LOG.debug("Done queuing work queue in {}ms.", STOPWATCH.lookAtTimeInMs());
      startWorkerThreads(workerThreads);
      completeWorkerThreads(workerThreads);
      LOG.debug("[Filter Verification] Finished in {}ms.", STOPWATCH.lookAtTimeInMs());

      LOG.debug("[Initialization 3] Start.");
      STOPWATCH.restart();
      hits = new AtomicInteger(0);
      workerThreads.clear();
      for (int workNum = 0; workNum < 10; ++workNum) {
          TesterThread workerThread = TesterThread.getFilter1EffectivenessThread(workQueue, filter);
          workerThreads.add(workerThread);
      }
      LOG.debug("[Initialization 3] Finished in {}ms.", STOPWATCH.lookAtTimeInMs());

      LOG.debug("[Filter Effectiveness] Start.");
      STOPWATCH.restart();
      populateWorkQueue(notInFilterElements, workQueue, workerThreads);
      LOG.debug("Done queuing work queue in {}ms.", STOPWATCH.lookAtTimeInMs());
      startWorkerThreads(workerThreads);
      completeWorkerThreads(workerThreads);
      LOG.debug("Hit {} of {} elements not in filter.", hits.get(), NUM_NOT_IN_FILTER_ELEMENTS);
      LOG.debug("[Filter Effectivenss] Finished in {}ms.", STOPWATCH.lookAtTimeInMs());
  }
  */

  private static void testBloomFilter2(
      MongoCollection<Document> inFilterElements, MongoCollection<Document> notInFilterElements)
      throws Exception {
    LOG.debug("------------------------------------------------");
    LOG.debug("- Beginning tests for bloom filter 2.");
    LOG.debug("------------------------------------------------");

    LOG.debug("[Initialization] Start.");
    STOPWATCH.restart();
    StringBloomFilter filter = new StringBloomFilter(NUM_BITS, NUM_BLOOM_HASHES);
    BlockingQueue<String> workQueue = new ArrayBlockingQueue<>(10_000_000);
    List<TesterThread> workerThreads = new ArrayList<>();
    for (int workNum = 0; workNum < 4; ++workNum) {
      TesterThread workerThread = TesterThread.getFilter2AddThread(workQueue, filter);
      workerThreads.add(workerThread);
    }
    long timeTakenMs = STOPWATCH.lookAtTimeInMs();
    LOG.debug("[Initialization] Finished in {}ms.", timeTakenMs);

    LOG.debug("[Filter Population] Start.");
    STOPWATCH.restart();
    populateWorkQueue(inFilterElements, workQueue, workerThreads);
    LOG.debug("Done queuing work queue in {}ms.", STOPWATCH.lookAtTimeInMs());
    startWorkerThreads(workerThreads);
    completeWorkerThreads(workerThreads);
    LOG.debug("[Filter Population] Finished in {}ms.", STOPWATCH.lookAtTimeInMs());

    LOG.debug("[Initialization 2] Start.");
    STOPWATCH.restart();
    workerThreads.clear();
    for (int workNum = 0; workNum < 10; ++workNum) {
      TesterThread workerThread = TesterThread.getFilter2VerifyThread(workQueue, filter);
      workerThreads.add(workerThread);
    }
    LOG.debug("[Initialization 2] Finished in {}ms.", STOPWATCH.lookAtTimeInMs());

    LOG.debug("[Filter Verification] Start.");
    STOPWATCH.restart();
    populateWorkQueue(inFilterElements, workQueue, workerThreads);
    LOG.debug("Done queuing work queue in {}ms.", STOPWATCH.lookAtTimeInMs());
    startWorkerThreads(workerThreads);
    completeWorkerThreads(workerThreads);
    LOG.debug("[Filter Verification] Finished in {}ms.", STOPWATCH.lookAtTimeInMs());

    LOG.debug("[Initialization 3] Start.");
    STOPWATCH.restart();
    hits = new AtomicInteger(0);
    workerThreads.clear();
    for (int workNum = 0; workNum < 10; ++workNum) {
      TesterThread workerThread = TesterThread.getFilter2EffectivenessThread(workQueue, filter);
      workerThreads.add(workerThread);
    }
    LOG.debug("[Initialization 3] Finished in {}ms.", STOPWATCH.lookAtTimeInMs());

    LOG.debug("[Filter Effectiveness] Start.");
    STOPWATCH.restart();
    populateWorkQueue(notInFilterElements, workQueue, workerThreads);
    LOG.debug("Done queuing work queue in {}ms.", STOPWATCH.lookAtTimeInMs());
    startWorkerThreads(workerThreads);
    completeWorkerThreads(workerThreads);
    LOG.debug("Hit {} of {} elements not in filter.", hits.get(), NUM_NOT_IN_FILTER_ELEMENTS);
    LOG.debug("[Filter Effectivenss] Finished in {}ms.", STOPWATCH.lookAtTimeInMs());
  }

  @SuppressWarnings("unchecked")
  private static void populateWorkQueue(
      MongoCollection<Document> collection,
      BlockingQueue<String> workQueue,
      List<TesterThread> workerThreads)
      throws Exception {
    MongoIterable<Document> iterable = collection.find();
    iterable.batchSize(10);
    MongoCursor<Document> cursor = iterable.iterator();
    try {
      int counter = 0;
      while (cursor.hasNext()) {
        List<String> elements = (List<String>) cursor.next().get("elements");
        for (String element : elements) {
          if (!workQueue.offer(element, 10, TimeUnit.MINUTES)) {
            throw new Exception("Failed to offer into workQueue.");
          }
          counter++;
          if (counter % 100_000 == 0) {
            LOG.debug(
                "Queued {} elements, capacity left is {}.", counter, workQueue.remainingCapacity());
          }
        }
      }
    } finally {
      for (TesterThread workerThread : workerThreads) {
        workerThread._moreInput = false;
      }
    }
  }

  private static void startWorkerThreads(List<TesterThread> workerThreads) throws Exception {
    LOG.debug("Restarting stopwatch to measure worker thread speed.");
    STOPWATCH.restart();
    for (TesterThread workerThread : workerThreads) {
      workerThread.start();
    }
  }

  private static void completeWorkerThreads(List<TesterThread> workerThreads) throws Exception {
    for (TesterThread workerThread : workerThreads) {
      workerThread.join();
    }
    for (TesterThread workerThread : workerThreads) {
      if (workerThread._success == false) {
        throw new Exception("Worker Thread failed.");
      }
    }
  }

  private static class TesterThread extends Thread {
    public enum Mode {
      FILTER1ADD,
      FILTER2ADD,
      FILTER1VERIFY,
      FILTER2VERIFY,
      FILTER1EFFECTIVENESS,
      FILTER2EFFECTIVENESS,
    }

    volatile boolean _moreInput = true;
    volatile boolean _success = false;

    private Mode _mode;
    private BlockingQueue<String> _workQueue;
    /*
    private BloomFilter<String> _filter1;
    */
    private StringBloomFilter _filter2;

    /*
    public static TesterThread getFilter1AddThread(BlockingQueue<String> workQueue,
                                                   BloomFilter<String> filter) {
        return new TesterThread(Mode.FILTER1ADD, workQueue, filter, null);
    }

    public static TesterThread getFilter1VerifyThread(BlockingQueue<String> workQueue,
                                                      BloomFilter<String> filter) {
        return new TesterThread(Mode.FILTER1VERIFY, workQueue, filter, null);
    }

    public static TesterThread getFilter1EffectivenessThread(BlockingQueue<String> workQueue,
                                                             BloomFilter<String> filter) {
        return new TesterThread(Mode.FILTER1EFFECTIVENESS, workQueue, filter, null);
    }
    */

    public static TesterThread getFilter2AddThread(
        BlockingQueue<String> workQueue, StringBloomFilter filter) {
      return new TesterThread(Mode.FILTER2ADD, workQueue, null, filter);
    }

    public static TesterThread getFilter2VerifyThread(
        BlockingQueue<String> workQueue, StringBloomFilter filter) {
      return new TesterThread(Mode.FILTER2VERIFY, workQueue, null, filter);
    }

    public static TesterThread getFilter2EffectivenessThread(
        BlockingQueue<String> workQueue, StringBloomFilter filter) {
      return new TesterThread(Mode.FILTER2EFFECTIVENESS, workQueue, null, filter);
    }

    public void run() {
      switch (_mode) {
          /*
          case FILTER1ADD:
              runFilter1Add();
              return;
          case FILTER1VERIFY:
              runFilter1Verify();
              return;
          case FILTER1EFFECTIVENESS:
              runFilter1Effectiveness();
              return;
          */
        case FILTER2ADD:
          runFilter2Add();
          return;
        case FILTER2VERIFY:
          runFilter2Verify();
          return;
        case FILTER2EFFECTIVENESS:
          runFilter2Effectiveness();
          return;
        default:
          return;
      }
    }

    /*
    private void runFilter1Add() {
        while (_workQueue.size() > 0 || _moreInput == true) {
            String element;
            try{
                element = _workQueue.poll(1, TimeUnit.SECONDS);
                if (element == null) {
                    continue;
                }
            } catch (InterruptedException interrupted) {
                LOG.warn("Unexpectedly interrupted while polling for blocks.", interrupted);
                return;
            }

            byte[] hashBytes = _filter1.getBytes(element);
            int[] hashes = BloomFilter.createHashes(hashBytes, NUM_BLOOM_HASHES);
            synchronized (_filter1) {
                FilterUtils.bloomSet(_filter1, hashes);
            }
        }

        _success = true;
    }

    private void runFilter1Verify() {
        while (_workQueue.size() > 0 || _moreInput == true) {
            String element;
            try{
                element = _workQueue.poll(1, TimeUnit.SECONDS);
                if (element == null) {
                    continue;
                }
            } catch (InterruptedException interrupted) {
                LOG.warn("Unexpectedly interrupted while polling for blocks.", interrupted);
                return;
            }

            if (!_filter1.contains(element)) {
                LOG.warn("Bloom filter 1 sucks because it doesn't work.");
                return;
            }
        }

        _success = true;
    }

    private void runFilter1Effectiveness() {
        while (_workQueue.size() > 0 || _moreInput == true) {
            String element;
            try{
                element = _workQueue.poll(1, TimeUnit.SECONDS);
                if (element == null) {
                    continue;
                }
            } catch (InterruptedException interrupted) {
                LOG.warn("Unexpectedly interrupted while polling for blocks.", interrupted);
                return;
            }

            if (_filter1.contains(element)) {
                hits.incrementAndGet();
            }
        }

        _success = true;
    }
    */

    private void runFilter2Add() {
      while (_workQueue.size() > 0 || _moreInput == true) {
        String element;
        try {
          element = _workQueue.poll(1, TimeUnit.SECONDS);
          if (element == null) {
            continue;
          }
        } catch (InterruptedException interrupted) {
          LOG.warn("Unexpectedly interrupted while polling for blocks.", interrupted);
          return;
        }

        _filter2.addElement(element);
      }

      _success = true;
    }

    private void runFilter2Verify() {
      while (_workQueue.size() > 0 || _moreInput == true) {
        String element;
        try {
          element = _workQueue.poll(1, TimeUnit.SECONDS);
          if (element == null) {
            continue;
          }
        } catch (InterruptedException interrupted) {
          LOG.warn("Unexpectedly interrupted while polling for blocks.", interrupted);
          return;
        }

        if (!_filter2.containsElement(element)) {
          LOG.warn("Bloom filter 2 sucks because it doesn't work.");
          return;
        }
      }

      _success = true;
    }

    private void runFilter2Effectiveness() {
      while (_workQueue.size() > 0 || _moreInput == true) {
        String element;
        try {
          element = _workQueue.poll(1, TimeUnit.SECONDS);
          if (element == null) {
            continue;
          }
        } catch (InterruptedException interrupted) {
          LOG.warn("Unexpectedly interrupted while polling for blocks.", interrupted);
          return;
        }

        if (_filter2.containsElement(element)) {
          hits.incrementAndGet();
        }
      }

      _success = true;
    }

    private TesterThread(
        Mode mode,
        BlockingQueue<String> workQueue,
        /*
        BloomFilter<String> filter1,
        */
        Object placeHolder,
        StringBloomFilter filter2) {
      _mode = mode;
      _workQueue = workQueue;
      /*
      _filter1 = filter1;
      */
      _filter2 = filter2;
    }
  }

  /** Helps generate random strings. */
  private static class Generator {
    private static final Random GENERATOR = new Random();

    public List<String> nextSHA256Hashes(int numHashes) throws Exception {
      MessageDigest md = MessageDigest.getInstance("SHA-256");
      List<String> hashes = new ArrayList<>(numHashes);
      byte[] data = new byte[400];
      for (int i = 0; i < numHashes; i++) {
        GENERATOR.nextBytes(data);
        StringBuilder ret = new StringBuilder();
        for (byte hashByte : md.digest(data)) ret.append(String.format("%02X", hashByte));
        hashes.add(ret.toString());
      }
      return hashes;
    }

    public List<String> nextStrings(int numStrings, int stringLength) {
      List<String> strings = new ArrayList<>(numStrings);
      for (int i = 0; i < numStrings; i++) {
        strings.add(nextString(stringLength));
      }
      return strings;
    }

    public void nextStrings(String[] strings, int stringLength) {
      for (int i = 0; i < strings.length; i++) {
        strings[i] = nextString(stringLength);
      }
    }

    public String nextString(int length) {
      byte[] data = new byte[length];
      GENERATOR.nextBytes(data);

      try {
        return new String(data, "US-ASCII");
      } catch (UnsupportedEncodingException e) {
        // There's no way it's unsupported, might as well print and exit though.
        LOG.debug("Cannot generate string:", e);
        throw new IllegalArgumentException("US-ASCII not supported for some reason.");
      }
    }
  }

  /** Used to keep track of time taken. */
  private static class Stopwatch {
    private long _startTimeMs;

    public Stopwatch() {
      restart();
    }

    public void restart() {
      _startTimeMs = System.currentTimeMillis();
    }

    public long lookAtTimeInMs() {
      return (System.currentTimeMillis() - _startTimeMs);
    }
  }
}
