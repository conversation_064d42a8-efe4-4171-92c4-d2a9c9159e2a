package com.xgen.cloud.federation.runtime.res.api.api_2023_01_01;

import static com.xgen.cloud.federation._public.view.ConnectedOrgConfigView.connectedOrgConfigToView;
import static java.util.stream.Collectors.joining;
import static org.hamcrest.CoreMatchers.notNullValue;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.allOf;
import static org.hamcrest.Matchers.hasProperty;
import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotEquals;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xgen.cloud.access.role._public.model.ConnectedOrgConfigRoleAssignment;
import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.activity._public.model.alert.config.AlertConfig;
import com.xgen.cloud.alerts.alert._private.dao.AlertConfigDao;
import com.xgen.cloud.alerts.checks.org._public.svc.OrgAlertConfig;
import com.xgen.cloud.federation._public.model.ConnectedOrgConfig;
import com.xgen.cloud.federation._public.model.FederationSettings;
import com.xgen.cloud.federation._public.model.IdentityProvider.IdpType;
import com.xgen.cloud.federation._public.model.OidcIdentityProvider;
import com.xgen.cloud.federation._public.model.RoleMapping;
import com.xgen.cloud.federation._public.model.SamlIdentityProvider;
import com.xgen.cloud.federation._public.model.SamlIdentityProvider.Status;
import com.xgen.cloud.federation._public.svc.ConnectedOrgConfigSvc;
import com.xgen.cloud.federation._public.view.ConnectedOrgConfigView;
import com.xgen.cloud.federation._public.view.ConnectedOrgConfigView.FieldDefs;
import com.xgen.cloud.federation._public.view.FederatedUserView;
import com.xgen.cloud.federation._public.view.RoleMappingView;
import com.xgen.cloud.notification._public.model.OrgNotification;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.organization._public.model.activity.OrgEvent.Type;
import com.xgen.cloud.user._private.dao.UserDao;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.module.federation.dao.FederationSettingsDao;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.GuiceTestRunner;
import com.xgen.svc.mms.api.res.ApiBaseResourceTest;
import com.xgen.svc.mms.api.res.common.ApiError;
import jakarta.inject.Inject;
import java.time.Duration;
import java.time.LocalDate;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.http.HttpStatus;
import org.bson.types.ObjectId;
import org.hamcrest.Matchers;
import org.hamcrest.core.Is;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

@RunWith(GuiceTestRunner.class)
public class ApiConnectedOrgConfigsResourceIntTests extends ApiBaseResourceTest {
  private static final String PATH_TEMPLATE =
      "/api/atlas/v2/federationSettings/%s/connectedOrgConfigs";
  private static final LocalDate V2_VERSION = LocalDate.of(2023, 1, 1);
  private static final ObjectMapper mapper = new ObjectMapper();

  @Inject private FederationSettingsDao federationSettingsDao;
  @Inject private UserDao userDao;
  @Inject private AlertConfigDao alertConfigDao;

  private Organization connectedOrg;
  private Organization secondConnectedOrg;
  private Organization thirdConnectedOrg;
  private AppUser connectedOrgOwner;
  private AppUser notConnectedOrgOwner;
  private AppUser connectedOrgMember;
  private ConnectedOrgConfig existingOrgConfig;
  private RoleMapping roleMapping;
  private ConnectedOrgConfig secondExistingOrgConfig;
  private ConnectedOrgConfig thirdConnectedOrgConfig;

  private Organization notConnectedOrg;
  private FederationSettings federationSettings;
  private FederationSettings lastOrgFederationSettings;
  private SamlIdentityProvider existingSamlIdentityProvider;
  private OidcIdentityProvider connectedOidcIdentityProvider;
  private OidcIdentityProvider unconnectedOidcIdentityProvider;
  private OidcIdentityProvider oidcWorkforceIdentityProviderWithoutDomains;
  private SamlIdentityProvider inactiveSamlIdentityProvider;
  private ObjectId connectedOidcIdpId;
  private ObjectId unconnectedOidcIdpId;
  private ObjectId oidcWorkforceIdpWithoutDomainsId;
  private String inactiveSamlIdentityProviderId;
  private String oktaIdpId;

  @Override
  @Before
  public void setUp() throws Exception {
    super.setUp();
    connectedOrg = MmsFactory.createOrganizationWithNDSPlan();
    secondConnectedOrg = MmsFactory.createOrganizationWithNDSPlan();
    thirdConnectedOrg = MmsFactory.createOrganizationWithNDSPlan();
    // Create an Organization that is not yet connected to federation to test addition of orgs
    notConnectedOrg = MmsFactory.createOrganizationWithNDSPlan();

    oktaIdpId = "oktaIdpId";
    connectedOidcIdpId = ObjectId.get();
    unconnectedOidcIdpId = ObjectId.get();
    oidcWorkforceIdpWithoutDomainsId = ObjectId.get();
    inactiveSamlIdentityProviderId = "inactiveSamlIdentityProviderOktaId";

    connectedOrgOwner =
        MmsFactory.createUserWithRoleInOrganization(
            connectedOrg, getUniquifier() + "@connected-org-tests.com", Role.ORG_OWNER);
    connectedOrgMember =
        MmsFactory.createUserWithRoleInOrganization(
            connectedOrg, getUniquifier() + "@domain.com", Role.ORG_MEMBER);

    notConnectedOrgOwner =
        MmsFactory.createUserWithRoleInOrganization(
            notConnectedOrg, getUniquifier() + "@connected-org-tests.com", Role.ORG_OWNER);

    userDao.addOrgIdAndAssignRoles(
        notConnectedOrgOwner.getId(),
        notConnectedOrg.getId(),
        Collections.singletonList(Role.ORG_OWNER));

    userDao.addOrgIdAndAssignRoles(
        connectedOrgOwner.getId(), connectedOrg.getId(), Collections.singletonList(Role.ORG_OWNER));

    roleMapping =
        new RoleMapping(
            ObjectId.get(),
            "admin",
            Set.of(ConnectedOrgConfigRoleAssignment.forOrg(Role.ORG_OWNER, connectedOrg.getId())));

    existingOrgConfig =
        new ConnectedOrgConfig(
            connectedOrg.getId(),
            Set.of(Role.ORG_MEMBER),
            oktaIdpId,
            Set.of(connectedOidcIdpId),
            true,
            Set.of("testdomain.com"),
            Set.of(roleMapping));
    secondExistingOrgConfig =
        new ConnectedOrgConfig(
            secondConnectedOrg.getId(),
            Set.of(Role.ORG_MEMBER),
            null,
            Collections.emptySet(),
            false,
            Set.of(),
            Set.of());
    thirdConnectedOrgConfig =
        new ConnectedOrgConfig(
            thirdConnectedOrg.getId(),
            Set.of(Role.ORG_MEMBER),
            null,
            Collections.emptySet(),
            false,
            Set.of(),
            Set.of());

    existingSamlIdentityProvider =
        SamlIdentityProvider.builder()
            .oktaIdpId("oktaIdpId")
            .associatedDomains(Set.of("domain.com"))
            .status(Status.ACTIVE)
            .build();
    connectedOidcIdentityProvider =
        OidcIdentityProvider.builder()
            .idpType(IdpType.WORKFORCE)
            .associatedDomains(Set.of("domain.com"))
            .id(connectedOidcIdpId)
            .build();
    unconnectedOidcIdentityProvider =
        OidcIdentityProvider.builder()
            .idpType(IdpType.WORKFORCE)
            .associatedDomains(Set.of("domain.com"))
            .id(unconnectedOidcIdpId)
            .build();
    oidcWorkforceIdentityProviderWithoutDomains =
        OidcIdentityProvider.builder()
            .idpType(IdpType.WORKFORCE)
            .id(oidcWorkforceIdpWithoutDomainsId)
            .build();
    inactiveSamlIdentityProvider =
        SamlIdentityProvider.builder().oktaIdpId(inactiveSamlIdentityProviderId).build();

    federationSettings =
        new FederationSettings(
            ObjectId.get(),
            Set.of(existingOrgConfig, secondExistingOrgConfig),
            Set.of(
                existingSamlIdentityProvider,
                connectedOidcIdentityProvider,
                unconnectedOidcIdentityProvider,
                oidcWorkforceIdentityProviderWithoutDomains,
                inactiveSamlIdentityProvider),
            null,
            false,
            new Date(),
            new Date());

    lastOrgFederationSettings =
        new FederationSettings(
            ObjectId.get(),
            Set.of(thirdConnectedOrgConfig),
            null,
            null,
            false,
            new Date(),
            new Date());

    federationSettingsDao.insertMajority(federationSettings);
    federationSettingsDao.insertMajority(lastOrgFederationSettings);
  }

  @Test
  public void testAddOrgConfig() throws JsonProcessingException {
    RoleMappingView roleMappingView =
        new RoleMappingView(
            null,
            Set.of(
                ConnectedOrgConfigRoleAssignment.forOrg(Role.ORG_MEMBER, notConnectedOrg.getId())));
    var connectedOrgConfigView =
        new ConnectedOrgConfigView(
            notConnectedOrg.getId(),
            Set.of(Role.ORG_OWNER),
            oktaIdpId,
            Collections.emptySet(),
            true,
            Set.of("my-allowed-domain.com"),
            Set.of(roleMappingView));
    runSaveTest(connectedOrgConfigView, null);
  }

  @Test
  public void testAddConnectedOrgConfig_validatesConnectedOrgConfigView()
      throws JsonProcessingException {
    var invalidRole = Role.GROUP_BACKUP_ADMIN;
    var invalidDomain = "";

    var connectedOrgConfigView =
        new ConnectedOrgConfigView(
            existingOrgConfig.getOrgId(),
            Set.of(invalidRole),
            oktaIdpId,
            Collections.emptySet(),
            false,
            Set.of(invalidDomain));

    JSONObject resp =
        doDigestJsonPostWithApiVersion(
            String.format(
                PATH_TEMPLATE + "/%s",
                federationSettings.getId(),
                connectedOrgConfigView.getOrgId()),
            connectedOrgConfigView,
            HttpStatus.SC_BAD_REQUEST,
            ADMIN_USERNAME,
            ADMIN_API_KEY,
            V2_VERSION);

    assertEquals("VALIDATION_ERROR", resp.get((ApiError.ERROR_CODE_FIELD)));
  }

  @Test
  public void testAddConnectedOrgConfig_cannotAddPostAuthRoleGrantsWhenNoIdp()
      throws JsonProcessingException {
    var connectedOrgConfigView =
        new ConnectedOrgConfigView(
            existingOrgConfig.getOrgId(),
            Set.of(Role.ORG_MEMBER),
            null,
            Collections.emptySet(),
            false,
            Set.of());

    JSONObject resp =
        doDigestJsonPostWithApiVersion(
            String.format(
                PATH_TEMPLATE + "/%s",
                federationSettings.getId(),
                connectedOrgConfigView.getOrgId()),
            connectedOrgConfigView,
            HttpStatus.SC_BAD_REQUEST,
            ADMIN_USERNAME,
            ADMIN_API_KEY,
            V2_VERSION);

    assertEquals("VALIDATION_ERROR", resp.get((ApiError.ERROR_CODE_FIELD)));
  }

  @Test
  public void testAddConnectedOrgConfig_cannotAddRoleMappingsWhenNoIdp()
      throws JsonProcessingException {
    RoleMappingView roleMappingView =
        new RoleMappingView(
            null,
            Set.of(
                ConnectedOrgConfigRoleAssignment.forOrg(Role.ORG_MEMBER, notConnectedOrg.getId())));
    var connectedOrgConfigView =
        new ConnectedOrgConfigView(
            existingOrgConfig.getOrgId(),
            null,
            null,
            Collections.emptySet(),
            false,
            Set.of(),
            Set.of(roleMappingView));

    JSONObject resp =
        doDigestJsonPostWithApiVersion(
            String.format(
                PATH_TEMPLATE + "/%s",
                federationSettings.getId(),
                connectedOrgConfigView.getOrgId()),
            connectedOrgConfigView,
            HttpStatus.SC_BAD_REQUEST,
            ADMIN_USERNAME,
            ADMIN_API_KEY,
            V2_VERSION);

    assertEquals("VALIDATION_ERROR", resp.get((ApiError.ERROR_CODE_FIELD)));
  }

  @Test
  public void testAddConnectedOrgConfig_validatesAllowedDomainNameLength()
      throws JsonProcessingException {
    var invalidHostname =
        Stream.generate(() -> "a")
            .limit(ConnectedOrgConfigSvc.ALLOWED_HOSTNAME_LENGTH + 1)
            .collect(joining());

    var connectedOrgConfigView =
        new ConnectedOrgConfigView(
            existingOrgConfig.getOrgId(),
            null,
            oktaIdpId,
            Collections.emptySet(),
            false,
            Set.of(invalidHostname));

    JSONObject resp =
        doDigestJsonPostWithApiVersion(
            String.format(
                PATH_TEMPLATE + "/%s",
                federationSettings.getId(),
                connectedOrgConfigView.getOrgId()),
            connectedOrgConfigView,
            HttpStatus.SC_BAD_REQUEST,
            ADMIN_USERNAME,
            ADMIN_API_KEY,
            V2_VERSION);

    assertEquals("VALIDATION_ERROR", resp.get((ApiError.ERROR_CODE_FIELD)));
  }

  @Test
  public void testAddConnectedOrgConfig_notOrgOwnerOfNewOrg() throws JsonProcessingException {
    var connectedOrgConfigView =
        new ConnectedOrgConfigView(
            notConnectedOrg.getId(),
            Set.of(Role.ORG_OWNER),
            null,
            Collections.emptySet(),
            true,
            Set.of("my-allowed-domain.com"));
    JSONObject resp =
        doDigestJsonPostWithApiVersion(
            String.format(
                PATH_TEMPLATE + "/%s",
                federationSettings.getId(),
                connectedOrgConfigView.getOrgId()),
            connectedOrgConfigView,
            HttpStatus.SC_UNAUTHORIZED,
            JANE_DOE_USERNAME,
            JANE_DOE_API_KEY,
            V2_VERSION);

    assertEquals(HttpStatus.SC_UNAUTHORIZED, resp.get((ApiError.ERROR_FIELD)));
  }

  @Test
  public void testAddConnectedOrgConfig_notOrgOwnerOfAlreadyConnectedOrg()
      throws JsonProcessingException {
    var connectedOrgConfigView =
        new ConnectedOrgConfigView(
            notConnectedOrg.getId(),
            Set.of(Role.ORG_OWNER),
            null,
            Collections.emptySet(),
            true,
            Set.of("my-allowed-domain.com"));
    doDigestJsonPostWithApiVersion(
        String.format(PATH_TEMPLATE + "/%s", federationSettings.getId(), notConnectedOrg.getId()),
        connectedOrgConfigView,
        HttpStatus.SC_UNAUTHORIZED,
        notConnectedOrgOwner.getUsername(),
        notConnectedOrgOwner.getPassword(),
        V2_VERSION);
  }

  @Test
  public void testAddConnectedOrgConfig_failsWhenOrgExists() throws JsonProcessingException {
    var connectedOrgConfigView =
        new ConnectedOrgConfigView(
            connectedOrg.getId(),
            Set.of(Role.ORG_OWNER),
            null,
            Collections.emptySet(),
            false,
            Set.of("my-allowed-domain.com", "additional-domain.org"));
    doDigestJsonPostWithApiVersion(
        String.format(
            PATH_TEMPLATE + "/%s", federationSettings.getId(), connectedOrgConfigView.getOrgId()),
        connectedOrgConfigView,
        HttpStatus.SC_BAD_REQUEST,
        ADMIN_USERNAME,
        ADMIN_API_KEY,
        V2_VERSION);
  }

  @Test
  public void testUpdateConnectedOrgConfig() throws JsonProcessingException {
    JSONObject requestObj = new JSONObject();
    requestObj.put("orgId", connectedOrg.getId());
    requestObj.put("postAuthRoleGrants", Set.of(Role.ORG_READ_ONLY));
    requestObj.put("domainAllowList", Set.of());
    requestObj.put("domainRestrictionEnabled", true);
    requestObj.put("identityProviderId", oktaIdpId);

    ConnectedOrgConfigView response =
        mapper.readValue(
            doDigestJsonPatchWithApiVersion(
                    String.format(
                        PATH_TEMPLATE + "/%s",
                        federationSettings.getId(),
                        secondConnectedOrg.getId()),
                    requestObj,
                    HttpStatus.SC_OK,
                    ADMIN_USERNAME,
                    ADMIN_API_KEY,
                    V2_VERSION)
                .toString(),
            ConnectedOrgConfigView.class);

    FederationSettings updatedFederationSettings =
        getFederationSettingsDao().find(federationSettings.getId()).orElseThrow();
    ConnectedOrgConfig updatedOrgConfig =
        updatedFederationSettings.getOrgConfigByOrgId(secondConnectedOrg.getId()).get();
    assertEquals(updatedOrgConfig.getRoleMappings(), secondExistingOrgConfig.getRoleMappings());
    assertEquals(updatedOrgConfig.getPostAuthRoleGrants(), Set.of(Role.ORG_READ_ONLY));
    assertEquals(updatedOrgConfig.getDomainAllowList(), Set.of());
    // Response view should still return domain from idp
    assertEquals(response.getDomainAllowList(), Set.of("domain.com"));
    assertTrue(updatedOrgConfig.isDomainRestrictionEnabled());
    assertEquals(updatedOrgConfig.getUiAccessIdentityProviderId(), oktaIdpId);
  }

  @Test
  public void testUpdateConnectedOrgConfig_disconnectDataAccessIdp()
      throws JsonProcessingException {
    var connectedOrgConfigView =
        new ConnectedOrgConfigView(
            connectedOrg.getId(), oktaIdpId, Set.of(), false, Collections.emptySet());

    ConnectedOrgConfigView response =
        mapper.readValue(
            doDigestJsonPatchWithApiVersion(
                    String.format(
                        PATH_TEMPLATE + "/%s", federationSettings.getId(), connectedOrg.getId()),
                    connectedOrgConfigView,
                    HttpStatus.SC_OK,
                    ADMIN_USERNAME,
                    ADMIN_API_KEY,
                    V2_VERSION)
                .toString(),
            ConnectedOrgConfigView.class);

    FederationSettings updatedFederationSettings =
        getFederationSettingsDao().find(federationSettings.getId()).orElseThrow();
    ConnectedOrgConfig updatedOrgConfig =
        updatedFederationSettings.getOrgConfigByOrgId(connectedOrg.getId()).get();
    assertEquals(updatedOrgConfig.getUiAccessIdentityProviderId(), oktaIdpId);
    assertEquals(updatedOrgConfig.getDataAccessIdentityProviderIds(), Collections.emptySet());
  }

  @Test
  public void testUpdateConnectedOrgConfig_switchConnectedDataAccessIdp()
      throws JsonProcessingException {
    var connectedOrgConfigView =
        new ConnectedOrgConfigView(
            connectedOrg.getId(),
            oktaIdpId,
            Set.of(unconnectedOidcIdpId),
            false,
            Collections.emptySet());

    ConnectedOrgConfigView response =
        mapper.readValue(
            doDigestJsonPatchWithApiVersion(
                    String.format(
                        PATH_TEMPLATE + "/%s", federationSettings.getId(), connectedOrg.getId()),
                    connectedOrgConfigView,
                    HttpStatus.SC_OK,
                    ADMIN_USERNAME,
                    ADMIN_API_KEY,
                    V2_VERSION)
                .toString(),
            ConnectedOrgConfigView.class);

    FederationSettings updatedFederationSettings =
        getFederationSettingsDao().find(federationSettings.getId()).orElseThrow();
    ConnectedOrgConfig updatedOrgConfig =
        updatedFederationSettings.getOrgConfigByOrgId(connectedOrg.getId()).get();
    assertEquals(updatedOrgConfig.getUiAccessIdentityProviderId(), oktaIdpId);
    assertEquals(updatedOrgConfig.getDataAccessIdentityProviderIds(), Set.of(unconnectedOidcIdpId));
  }

  @Test
  public void testUpdateConnectedOrgConfig_cannotUpdatePostAuthRoleGrantsWhenNoIdp() {
    JSONObject requestObj = new JSONObject();
    requestObj.put("orgId", secondConnectedOrg.getId());
    requestObj.put("postAuthRoleGrants", Set.of(Role.ORG_READ_ONLY));
    requestObj.put("domainRestrictionEnabled", true);

    JSONObject response =
        doDigestJsonPatchWithApiVersion(
            String.format(
                PATH_TEMPLATE + "/%s", federationSettings.getId(), secondConnectedOrg.getId()),
            requestObj,
            HttpStatus.SC_BAD_REQUEST,
            ADMIN_USERNAME,
            ADMIN_API_KEY,
            V2_VERSION);

    assertEquals("VALIDATION_ERROR", response.get((ApiError.ERROR_CODE_FIELD)));
  }

  @Test
  public void testUpdateConnectedOrgConfig_invalidDataAccessIdpIds() {
    var connectedOrgConfigView =
        new ConnectedOrgConfigView(
            connectedOrg.getId(), null, Set.of(ObjectId.get()), false, Collections.emptySet());

    JSONObject response =
        doDigestJsonPatchWithApiVersion(
            String.format(PATH_TEMPLATE + "/%s", federationSettings.getId(), connectedOrg.getId()),
            connectedOrgConfigView,
            HttpStatus.SC_BAD_REQUEST,
            ADMIN_USERNAME,
            ADMIN_API_KEY,
            V2_VERSION);

    assertEquals("VALIDATION_ERROR", response.get((ApiError.ERROR_CODE_FIELD)));
    assertTrue(
        response
            .getJSONObject("errors")
            .has(ConnectedOrgConfigView.FieldDefs.DATA_ACCESS_IDENTITY_PROVIDER_IDS));
    assertEquals(
        response
            .getJSONObject("errors")
            .get(ConnectedOrgConfigView.FieldDefs.DATA_ACCESS_IDENTITY_PROVIDER_IDS),
        "must contain only valid data access identity providers in the federation");
  }

  @Test
  public void testUpdateConnectedOrgConfig_cannotConnectTwoOidcWorkforceIdps() {
    var connectedOrgConfigView =
        new ConnectedOrgConfigView(
            connectedOrg.getId(),
            null,
            Set.of(connectedOidcIdpId, unconnectedOidcIdpId),
            false,
            Collections.emptySet());

    JSONObject response =
        doDigestJsonPatchWithApiVersion(
            String.format(PATH_TEMPLATE + "/%s", federationSettings.getId(), connectedOrg.getId()),
            connectedOrgConfigView,
            HttpStatus.SC_BAD_REQUEST,
            ADMIN_USERNAME,
            ADMIN_API_KEY,
            V2_VERSION);

    assertEquals("VALIDATION_ERROR", response.get((ApiError.ERROR_CODE_FIELD)));
    assertTrue(
        response
            .getJSONObject("errors")
            .has(ConnectedOrgConfigView.FieldDefs.DATA_ACCESS_IDENTITY_PROVIDER_IDS));
    assertEquals(
        response
            .getJSONObject("errors")
            .get(ConnectedOrgConfigView.FieldDefs.DATA_ACCESS_IDENTITY_PROVIDER_IDS),
        "cannot attempt to connect more than one OIDC workforce IdP");
  }

  @Test
  public void testUpdateConnectedOrgConfig_invalidUiAccessIdpId() {
    var connectedOrgConfigView =
        new ConnectedOrgConfigView(
            connectedOrg.getId(),
            "invalidId",
            Collections.emptySet(),
            false,
            Collections.emptySet());

    JSONObject response =
        doDigestJsonPatchWithApiVersion(
            String.format(PATH_TEMPLATE + "/%s", federationSettings.getId(), connectedOrg.getId()),
            connectedOrgConfigView,
            HttpStatus.SC_BAD_REQUEST,
            ADMIN_USERNAME,
            ADMIN_API_KEY,
            V2_VERSION);

    assertEquals("VALIDATION_ERROR", response.get((ApiError.ERROR_CODE_FIELD)));
    assertTrue(response.getJSONObject("errors").has(FieldDefs.IDENTITY_PROVIDER_ID));
    assertEquals(
        response.getJSONObject("errors").get(FieldDefs.IDENTITY_PROVIDER_ID),
        "must be a valid identity provider in the federation");
  }

  @Test
  public void testUpdateConnectedOrgConfig_cannotConnectInactiveSamlIdp() {
    var connectedOrgConfigView =
        new ConnectedOrgConfigView(
            connectedOrg.getId(),
            inactiveSamlIdentityProviderId,
            Collections.emptySet(),
            false,
            Collections.emptySet());

    JSONObject response =
        doDigestJsonPatchWithApiVersion(
            String.format(PATH_TEMPLATE + "/%s", federationSettings.getId(), connectedOrg.getId()),
            connectedOrgConfigView,
            HttpStatus.SC_BAD_REQUEST,
            ADMIN_USERNAME,
            ADMIN_API_KEY,
            V2_VERSION);

    assertEquals("VALIDATION_ERROR", response.get((ApiError.ERROR_CODE_FIELD)));
    assertTrue(response.getJSONObject("errors").has(FieldDefs.IDENTITY_PROVIDER_ID));
    assertEquals(
        response.getJSONObject("errors").get(FieldDefs.IDENTITY_PROVIDER_ID),
        "must have associated domains and be activated before it can be connected to an"
            + " organization");
  }

  @Test
  public void testUpdateConnectedOrgConfig_emptyPostAuthsCountsAsUpdate() {
    JSONObject requestObj = new JSONObject();
    requestObj.put("orgId", secondConnectedOrg.getId());
    requestObj.put("postAuthRoleGrants", Set.of());
    requestObj.put("domainRestrictionEnabled", true);

    JSONObject response =
        doDigestJsonPatchWithApiVersion(
            String.format(
                PATH_TEMPLATE + "/%s", federationSettings.getId(), secondConnectedOrg.getId()),
            requestObj,
            HttpStatus.SC_BAD_REQUEST,
            ADMIN_USERNAME,
            ADMIN_API_KEY,
            V2_VERSION);

    assertEquals("VALIDATION_ERROR", response.get((ApiError.ERROR_CODE_FIELD)));
  }

  @Test
  public void testUpdateConnectedOrgConfig_whenFieldsNotIncluded() {
    JSONObject requestObj = new JSONObject();
    requestObj.put("orgId", connectedOrg.getId());
    requestObj.put("domainRestrictionEnabled", true);

    doDigestJsonPatchWithApiVersion(
        String.format(PATH_TEMPLATE + "/%s", federationSettings.getId(), connectedOrg.getId()),
        requestObj,
        HttpStatus.SC_OK,
        ADMIN_USERNAME,
        ADMIN_API_KEY,
        V2_VERSION);

    FederationSettings updatedFederationSettings =
        getFederationSettingsDao().find(federationSettings.getId()).orElseThrow();
    ConnectedOrgConfig updatedOrgConfig =
        updatedFederationSettings.getOrgConfigByOrgId(connectedOrg.getId()).get();

    // When no idp id passed in, disconnect org from idp
    assertNull(updatedOrgConfig.getUiAccessIdentityProviderId());
    // When no role mappings are passed in, no update
    assertEquals(updatedOrgConfig.getRoleMappings(), existingOrgConfig.getRoleMappings());
    // When no post role auth grants are passed in, no update
    assertEquals(
        updatedOrgConfig.getPostAuthRoleGrants(), existingOrgConfig.getPostAuthRoleGrants());
    // When no allowed domains are passed in, no update
    assertEquals(updatedOrgConfig.getDomainAllowList(), existingOrgConfig.getDomainAllowList());
    assertTrue(updatedOrgConfig.isDomainRestrictionEnabled());
  }

  @Test
  public void testUpdateConnectedOrgConfig_updateIdp_canUpdatePostAuthRoleGrants() {
    JSONObject requestObj = new JSONObject();
    requestObj.put("orgId", connectedOrg.getId());
    requestObj.put("domainRestrictionEnabled", true);
    requestObj.put("identityProviderId", existingSamlIdentityProvider.getOktaIdpId());
    requestObj.put("postAuthRoleGrants", Set.of(Role.ORG_READ_ONLY));

    doDigestJsonPatchWithApiVersion(
        String.format(PATH_TEMPLATE + "/%s", federationSettings.getId(), connectedOrg.getId()),
        requestObj,
        HttpStatus.SC_OK,
        ADMIN_USERNAME,
        ADMIN_API_KEY,
        V2_VERSION);

    FederationSettings updatedFederationSettings =
        getFederationSettingsDao().find(federationSettings.getId()).orElseThrow();
    ConnectedOrgConfig updatedOrgConfig =
        updatedFederationSettings.getOrgConfigByOrgId(connectedOrg.getId()).get();

    assertEquals(
        updatedOrgConfig.getUiAccessIdentityProviderId(),
        existingSamlIdentityProvider.getOktaIdpId());
    assertTrue(updatedOrgConfig.isDomainRestrictionEnabled());
    // Post auth role grants can be updated with existing IdP
    assertEquals(updatedOrgConfig.getPostAuthRoleGrants(), Set.of(Role.ORG_READ_ONLY));
    // No change if fields were excluded
    assertEquals(updatedOrgConfig.getRoleMappings(), existingOrgConfig.getRoleMappings());
    assertEquals(updatedOrgConfig.getDomainAllowList(), existingOrgConfig.getDomainAllowList());
  }

  @Test
  public void testUpdateConnectedOrgConfig_failsWhenIdpIsNotValid() {
    JSONObject requestObj = new JSONObject();
    requestObj.put("orgId", connectedOrg.getId());
    requestObj.put("domainRestrictionEnabled", true);
    requestObj.put("identityProviderId", "123");

    JSONObject resp =
        doDigestJsonPatchWithApiVersion(
            String.format(PATH_TEMPLATE + "/%s", federationSettings.getId(), connectedOrg.getId()),
            requestObj,
            HttpStatus.SC_BAD_REQUEST,
            ADMIN_USERNAME,
            ADMIN_API_KEY,
            V2_VERSION);

    assertEquals("VALIDATION_ERROR", resp.getString((ApiError.ERROR_CODE_FIELD)));
    assertTrue(resp.getJSONObject("errors").has("identityProviderId"));
  }

  @Test
  public void testRemoveConnectedOrgConfig() {
    doDigestJsonDeleteWithApiVersion(
        String.format(
            PATH_TEMPLATE + "/%s", federationSettings.getId(), secondConnectedOrg.getId()),
        HttpStatus.SC_NO_CONTENT,
        ADMIN_USERNAME,
        ADMIN_API_KEY,
        V2_VERSION);

    FederationSettings updatedFederationSettings =
        getFederationSettingsDao().find(federationSettings.getId()).orElseThrow();

    Set<ConnectedOrgConfig> connectedOrgConfigs =
        updatedFederationSettings.getConnectedOrgConfigs();

    assertFalse(connectedOrgConfigs.contains(secondExistingOrgConfig));
  }

  @Test
  public void testRemoveConnectedOrgConfig_failsWhenNotOrgOwner() {
    JSONObject resp =
        doDigestJsonDeleteWithApiVersion(
            String.format(
                PATH_TEMPLATE + "/%s", federationSettings.getId(), secondConnectedOrg.getId()),
            HttpStatus.SC_UNAUTHORIZED,
            JANE_DOE_USERNAME,
            JANE_DOE_API_KEY,
            V2_VERSION);

    assertEquals(HttpStatus.SC_UNAUTHORIZED, resp.get((ApiError.ERROR_FIELD)));
  }

  @Test
  public void testRemoveConnectedOrgConfig_failsWhenLastOrg() {
    JSONObject resp =
        doDigestJsonDeleteWithApiVersion(
            String.format(
                PATH_TEMPLATE + "/%s",
                lastOrgFederationSettings.getId(),
                thirdConnectedOrg.getId()),
            HttpStatus.SC_BAD_REQUEST,
            ADMIN_USERNAME,
            ADMIN_API_KEY,
            V2_VERSION);

    assertEquals("CANNOT_DELETE_LAST_FEDERATED_ORG", resp.get((ApiError.ERROR_CODE_FIELD)));
  }

  @Test
  public void testGetConnectedOrgConfig() throws JsonProcessingException {
    ConnectedOrgConfigView responseConnectedOrgConfig =
        mapper.readValue(
            doDigestJsonGetWithApiVersion(
                    String.format(
                        PATH_TEMPLATE + "/%s",
                        federationSettings.getId(),
                        secondExistingOrgConfig.getOrgId()),
                    HttpStatus.SC_OK,
                    ADMIN_USERNAME,
                    ADMIN_API_KEY,
                    V2_VERSION)
                .toString(),
            ConnectedOrgConfigView.class);

    assertEquals(responseConnectedOrgConfig.getOrgId(), secondExistingOrgConfig.getOrgId());
    assertEquals(
        responseConnectedOrgConfig.getIdentityProviderId(),
        secondExistingOrgConfig.getUiAccessIdentityProviderId());
    assertEquals(
        responseConnectedOrgConfig.getPostAuthRoleGrants(),
        secondExistingOrgConfig.getPostAuthRoleGrants());
    assertEquals(
        responseConnectedOrgConfig.getDomainAllowList(),
        secondExistingOrgConfig.getDomainAllowList());
    assertEquals(
        responseConnectedOrgConfig.getRoleMappings(),
        secondExistingOrgConfig.getRoleMappings().stream()
            .map(RoleMappingView::roleMappingToView)
            .collect(Collectors.toSet()));
    assertNull(responseConnectedOrgConfig.getUserConflicts());
  }

  @Test
  public void testGetConnectedOrgConfig_withUserConflicts() throws JsonProcessingException {
    ConnectedOrgConfigView responseConnectedOrgConfig =
        mapper.readValue(
            doDigestJsonGetWithApiVersion(
                    String.format(
                        PATH_TEMPLATE + "/%s",
                        federationSettings.getId(),
                        existingOrgConfig.getOrgId()),
                    HttpStatus.SC_OK,
                    ADMIN_USERNAME,
                    ADMIN_API_KEY,
                    V2_VERSION)
                .toString(),
            ConnectedOrgConfigView.class);

    assertEquals(responseConnectedOrgConfig.getOrgId(), existingOrgConfig.getOrgId());
    assertEquals(
        responseConnectedOrgConfig.getIdentityProviderId(),
        existingOrgConfig.getUiAccessIdentityProviderId());
    assertEquals(
        responseConnectedOrgConfig.getPostAuthRoleGrants(),
        existingOrgConfig.getPostAuthRoleGrants());
    assertNotEquals(
        responseConnectedOrgConfig.getDomainAllowList(), existingOrgConfig.getDomainAllowList());
    assertEquals(
        responseConnectedOrgConfig.getDomainAllowList(), Set.of("domain.com", "testdomain.com"));
    assertEquals(
        responseConnectedOrgConfig.getRoleMappings(),
        existingOrgConfig.getRoleMappings().stream()
            .map(RoleMappingView::roleMappingToView)
            .collect(Collectors.toSet()));
    assertEquals(
        responseConnectedOrgConfig.getUserConflicts().get(0),
        new FederatedUserView(
            connectedOrgOwner.getId(),
            connectedOrgOwner.getPrimaryEmail(),
            connectedOrgOwner.getFirstName(),
            connectedOrgOwner.getLastName(),
            federationSettings.getId()));
    assertFalse(
        responseConnectedOrgConfig
            .getUserConflicts()
            .contains(
                new FederatedUserView(
                    connectedOrgMember.getId(),
                    connectedOrgMember.getPrimaryEmail(),
                    connectedOrgMember.getFirstName(),
                    connectedOrgMember.getLastName(),
                    federationSettings.getId())));
  }

  @Test
  public void testGetConnectedOrgConfig_failsWhenNotFound() {
    JSONObject resp =
        doDigestJsonGetWithApiVersion(
            String.format(PATH_TEMPLATE + "/%s", federationSettings.getId(), ObjectId.get()),
            HttpStatus.SC_NOT_FOUND,
            ADMIN_USERNAME,
            ADMIN_API_KEY,
            V2_VERSION);

    assertEquals(HttpStatus.SC_NOT_FOUND, resp.getInt((ApiError.ERROR_FIELD)));
  }

  @Test
  public void testGetConnectedOrgConfig_failsWhenNotOrgOwner() {
    JSONObject resp =
        doDigestJsonGetWithApiVersion(
            String.format(
                PATH_TEMPLATE + "/%s", federationSettings.getId(), existingOrgConfig.getOrgId()),
            HttpStatus.SC_UNAUTHORIZED,
            JOHN_DOE_USERNAME,
            JOHN_DOE_API_KEY,
            V2_VERSION);

    assertEquals(HttpStatus.SC_UNAUTHORIZED, resp.getInt((ApiError.ERROR_FIELD)));
  }

  @Test
  public void testGetAllConnectedOrgConfigs() {
    JSONObject resp =
        doDigestJsonGetWithApiVersion(
            String.format(PATH_TEMPLATE, federationSettings.getId()),
            HttpStatus.SC_OK,
            ADMIN_USERNAME,
            ADMIN_API_KEY,
            V2_VERSION);

    JSONArray results = resp.getJSONArray("results");
    Set<ConnectedOrgConfig> connectedOrgConfigs = federationSettings.getConnectedOrgConfigs();

    assertEquals(results.length(), connectedOrgConfigs.size());
  }

  @Test
  public void testGetAllConnectedOrgConfigs_failsWhenNotOrgOwner() {
    JSONObject resp =
        doDigestJsonGetWithApiVersion(
            String.format(PATH_TEMPLATE, federationSettings.getId()),
            HttpStatus.SC_FORBIDDEN,
            JANE_DOE_USERNAME,
            JANE_DOE_API_KEY,
            V2_VERSION);

    assertEquals(HttpStatus.SC_FORBIDDEN, resp.get((ApiError.ERROR_FIELD)));
  }

  @Test
  public void testGetAllRoleMappings() {
    JSONObject resp =
        doDigestJsonGetWithApiVersion(
            String.format(
                PATH_TEMPLATE + "/%s/roleMappings",
                federationSettings.getId(),
                existingOrgConfig.getOrgId()),
            HttpStatus.SC_OK,
            ADMIN_USERNAME,
            ADMIN_API_KEY,
            V2_VERSION);

    JSONArray results = resp.getJSONArray("results");
    Set<RoleMapping> roleMappings =
        federationSettings
            .getOrgConfigByOrgId(existingOrgConfig.getOrgId())
            .get()
            .getRoleMappings();

    assertEquals(results.length(), roleMappings.size());
  }

  @Test
  public void testGetAllRoleMappings_failsWhenNotOrgOwner() {
    JSONObject resp =
        doDigestJsonGetWithApiVersion(
            String.format(
                PATH_TEMPLATE + "/%s/roleMappings",
                federationSettings.getId(),
                existingOrgConfig.getOrgId()),
            HttpStatus.SC_UNAUTHORIZED,
            JANE_DOE_USERNAME,
            JANE_DOE_API_KEY,
            V2_VERSION);

    assertEquals(HttpStatus.SC_UNAUTHORIZED, resp.get((ApiError.ERROR_FIELD)));
  }

  @Test
  public void testAddRoleMapping() throws JsonProcessingException {
    var requestNewRoleMappingView =
        new RoleMappingView(
            "other",
            Set.of(ConnectedOrgConfigRoleAssignment.forOrg(Role.ORG_MEMBER, connectedOrg.getId())));

    RoleMappingView responseNewRoleMappingView =
        mapper.readValue(
            doDigestJsonPostWithApiVersion(
                    String.format(
                        PATH_TEMPLATE + "/%s/roleMappings",
                        federationSettings.getId(),
                        existingOrgConfig.getOrgId()),
                    requestNewRoleMappingView,
                    HttpStatus.SC_OK,
                    ADMIN_USERNAME,
                    ADMIN_API_KEY,
                    V2_VERSION)
                .toString(),
            RoleMappingView.class);

    assertThat(
        responseNewRoleMappingView,
        allOf(
            Is.is(notNullValue()),
            hasProperty("id", Is.is(notNullValue())),
            hasProperty(
                "externalGroupName", Is.is(requestNewRoleMappingView.getExternalGroupName())),
            hasProperty("roleAssignments", Is.is(requestNewRoleMappingView.getRoleAssignments()))));

    FederationSettings updatedFederationSettings =
        getFederationSettingsDao().find(federationSettings.getId()).orElseThrow();
    ConnectedOrgConfig orgConfig =
        updatedFederationSettings.getOrgConfigByOrgId(connectedOrg.getId()).orElseThrow();
    RoleMapping roleMapping =
        new RoleMapping(
            responseNewRoleMappingView.getId(),
            responseNewRoleMappingView.getExternalGroupName(),
            responseNewRoleMappingView.getRoleAssignments());

    assertThat(orgConfig.getRoleMappings(), hasSize(2));
    assertTrue(orgConfig.getRoleMappings().contains(roleMapping));
  }

  @Test
  public void testAddRoleMapping_failsWhenRequestBodyNull() throws JsonProcessingException {
    JSONObject resp =
        doDigestJsonPostWithApiVersion(
            String.format(
                PATH_TEMPLATE + "/%s/roleMappings",
                federationSettings.getId(),
                existingOrgConfig.getOrgId()),
            (Object) null,
            HttpStatus.SC_BAD_REQUEST,
            ADMIN_USERNAME,
            ADMIN_API_KEY,
            V2_VERSION);

    assertEquals(HttpStatus.SC_BAD_REQUEST, resp.getInt((ApiError.ERROR_FIELD)));
  }

  @Test
  public void testUpdateRoleMapping() throws JsonProcessingException {
    var requestRoleMappingView =
        new RoleMappingView(
            roleMapping.getId(),
            "other",
            Set.of(ConnectedOrgConfigRoleAssignment.forOrg(Role.ORG_OWNER, connectedOrg.getId())));

    RoleMappingView responseRoleMappingView =
        mapper.readValue(
            doDigestJsonPutWithApiVersion(
                    String.format(
                        PATH_TEMPLATE + "/%s/roleMappings/%s",
                        federationSettings.getId(),
                        existingOrgConfig.getOrgId(),
                        requestRoleMappingView.getId()),
                    requestRoleMappingView,
                    HttpStatus.SC_OK,
                    ADMIN_USERNAME,
                    ADMIN_API_KEY,
                    V2_VERSION)
                .toString(),
            RoleMappingView.class);

    assertThat(
        responseRoleMappingView,
        allOf(
            Is.is(notNullValue()),
            hasProperty("id", Is.is(notNullValue())),
            hasProperty("externalGroupName", Is.is(requestRoleMappingView.getExternalGroupName())),
            hasProperty("roleAssignments", Is.is(requestRoleMappingView.getRoleAssignments()))));

    FederationSettings updatedFederationSettings =
        getFederationSettingsDao().find(federationSettings.getId()).orElseThrow();
    ConnectedOrgConfig orgConfig =
        updatedFederationSettings.getOrgConfigByOrgId(connectedOrg.getId()).orElseThrow();
    RoleMapping roleMapping =
        new RoleMapping(
            responseRoleMappingView.getId(),
            responseRoleMappingView.getExternalGroupName(),
            responseRoleMappingView.getRoleAssignments());

    assertThat(orgConfig.getRoleMappings(), hasSize(1));
    assertTrue(orgConfig.getRoleMappings().contains(roleMapping));
  }

  @Test
  public void testDeleteRoleMapping() {
    var requestRoleMappingView =
        new RoleMappingView(
            roleMapping.getId(),
            "other",
            Set.of(ConnectedOrgConfigRoleAssignment.forOrg(Role.ORG_OWNER, connectedOrg.getId())));

    doDigestJsonDeleteWithApiVersion(
        String.format(
            PATH_TEMPLATE + "/%s/roleMappings/%s",
            federationSettings.getId(),
            existingOrgConfig.getOrgId(),
            requestRoleMappingView.getId()),
        HttpStatus.SC_NO_CONTENT,
        ADMIN_USERNAME,
        ADMIN_API_KEY,
        V2_VERSION);

    FederationSettings updatedFederationSettings =
        getFederationSettingsDao().find(federationSettings.getId()).orElseThrow();
    ConnectedOrgConfig orgConfig =
        updatedFederationSettings.getOrgConfigByOrgId(connectedOrg.getId()).orElseThrow();
    RoleMapping roleMapping =
        new RoleMapping(
            requestRoleMappingView.getId(),
            requestRoleMappingView.getExternalGroupName(),
            requestRoleMappingView.getRoleAssignments());

    assertThat(orgConfig.getRoleMappings(), hasSize(0));
    assertFalse(orgConfig.getRoleMappings().contains(roleMapping));
  }

  @Test
  public void testGetRoleMapping() throws JsonProcessingException {
    RoleMappingView responseRoleMappingView =
        mapper.readValue(
            doDigestJsonGetWithApiVersion(
                    String.format(
                        PATH_TEMPLATE + "/%s/roleMappings/%s",
                        federationSettings.getId(),
                        existingOrgConfig.getOrgId(),
                        roleMapping.getId()),
                    HttpStatus.SC_OK,
                    ADMIN_USERNAME,
                    ADMIN_API_KEY,
                    V2_VERSION)
                .toString(),
            RoleMappingView.class);

    assertThat(
        responseRoleMappingView,
        allOf(
            Is.is(notNullValue()),
            hasProperty("id", Is.is(notNullValue())),
            hasProperty("externalGroupName", Is.is(roleMapping.getExternalGroupName())),
            hasProperty("roleAssignments", Is.is(roleMapping.getRoleAssignments()))));

    ConnectedOrgConfig orgConfig =
        federationSettings.getOrgConfigByOrgId(connectedOrg.getId()).orElseThrow();
    RoleMapping roleMapping =
        new RoleMapping(
            responseRoleMappingView.getId(),
            responseRoleMappingView.getExternalGroupName(),
            responseRoleMappingView.getRoleAssignments());

    assertThat(orgConfig.getRoleMappings(), hasSize(1));
    assertTrue(orgConfig.getRoleMappings().contains(roleMapping));
  }

  private void runSaveTest(ConnectedOrgConfigView pView, ConnectedOrgConfig pExistingConfig)
      throws JsonProcessingException {
    ConnectedOrgConfigView responseOrgConfig =
        mapper.readValue(
            doDigestJsonPostWithApiVersion(
                    String.format(
                        PATH_TEMPLATE + "/%s", federationSettings.getId(), pView.getOrgId()),
                    pView,
                    HttpStatus.SC_OK,
                    ADMIN_USERNAME,
                    ADMIN_API_KEY,
                    V2_VERSION)
                .toString(),
            ConnectedOrgConfigView.class);

    var roleMappingsMatcher =
        pExistingConfig == null
            ? is(Matchers.emptyCollectionOf(RoleMappingView.class))
            : is(pExistingConfig.getRoleMappings());

    assertThat(
        responseOrgConfig,
        allOf(
            hasProperty("orgId", is(pView.getOrgId())),
            hasProperty("postAuthRoleGrants", is(pView.getPostAuthRoleGrants())),
            hasProperty("identityProviderId", is(pView.getIdentityProviderId())),
            hasProperty("domainRestrictionEnabled", is(pView.isDomainRestrictionEnabled())),
            hasProperty("domainAllowList", is(pView.getDomainAllowList())),
            hasProperty("roleMappings", roleMappingsMatcher)));

    var persistedOrgConfig =
        getFederationSettingsDao()
            .find(federationSettings.getId())
            .orElseThrow()
            .getOrgConfigByOrgId(pView.getOrgId())
            .orElseThrow();

    assertThat(connectedOrgConfigToView(persistedOrgConfig), is(responseOrgConfig));

    Organization organization = pExistingConfig != null ? connectedOrg : notConnectedOrg;
    OrgAlertConfig expectedAlertConfig = createExpectedAlertConfig(organization);
    List<AlertConfig> alertConfigs = getAlertConfigDao().findByOrgId(organization.getId());
    assertThat(alertConfigs, hasSize(1));
    assertThat(
        alertConfigs.get(0),
        allOf(
            hasProperty("typeName", is(expectedAlertConfig.getTypeName())),
            hasProperty("orgId", is(expectedAlertConfig.getOrgId())),
            hasProperty("notifications", is(expectedAlertConfig.getNotifications()))));
  }

  private OrgAlertConfig createExpectedAlertConfig(Organization pOrganization) {
    OrgAlertConfig.Builder expectedAlertConfigBuilder =
        new OrgAlertConfig.Builder(Type.ORG_IDP_CERTIFICATE_ABOUT_TO_EXPIRE);
    expectedAlertConfigBuilder.orgId(pOrganization.getId());
    OrgNotification orgOwnerNotification =
        OrgNotification.newNotificationWithRoles(
            pOrganization.getId(),
            pOrganization.getName(),
            (int) Duration.ofDays(1).toMinutes(),
            Role.ORG_OWNER);
    expectedAlertConfigBuilder.notification(orgOwnerNotification);
    return expectedAlertConfigBuilder.build();
  }

  private FederationSettingsDao getFederationSettingsDao() {
    return federationSettingsDao;
  }

  private AlertConfigDao getAlertConfigDao() {
    return alertConfigDao;
  }
}
