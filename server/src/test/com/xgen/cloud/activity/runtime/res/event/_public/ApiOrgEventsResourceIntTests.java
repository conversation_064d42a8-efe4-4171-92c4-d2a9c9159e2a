package com.xgen.cloud.activity.runtime.res.event._public;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.containsInAnyOrder;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

import com.xgen.cloud.activity._public.model.event.Event;
import com.xgen.cloud.common.util._public.time.TimeUtils2;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.svc.common.TestDataUtils;
import com.xgen.svc.core.GuiceTestRunner;
import com.xgen.svc.mms.api.res.ApiBaseResourceTest;
import com.xgen.svc.mms.model.billing.OrgPlan;
import com.xgen.svc.mms.model.billing.OrgPrepaidPlan;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;
import java.util.Spliterator;
import java.util.Spliterators;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.stream.StreamSupport;
import org.apache.commons.lang.time.DateUtils;
import org.apache.http.HttpStatus;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

@RunWith(GuiceTestRunner.class)
public class ApiOrgEventsResourceIntTests extends ApiBaseResourceTest {

  private static final String RAW_EVENT_FIELD = "raw";

  @Before
  @Override
  public void setUp() throws Exception {
    super.setUp();
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/OrganizationDao/organizations.json.ftl",
        getParamMap(),
        Organization.DB_NAME,
        Organization.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/billing/OrgPlanDao/plans.json.ftl",
        null,
        OrgPlan.DB_NAME,
        OrgPlan.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/billing/OrgPrepaidPlanDao/prepaidPlans.json.ftl",
        null,
        OrgPrepaidPlan.DB_NAME,
        OrgPrepaidPlan.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/GroupDao/groups.json.ftl", getParamMap(), Group.DB_NAME, Group.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/event/EventDao/api_events.json.ftl",
        getParamMap(),
        Event.DB_NAME,
        Event.COLLECTION_NAME);
  }

  @Test
  public void testGetOrgEvent() {
    // test if admin can access the endpoint and hidden event
    final JSONObject resp1 =
        doDigestJsonGet(
            "api/public/v1.0/orgs/" + oid(200) + "/events/" + oid(35),
            HttpStatus.SC_OK,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    // test whether the event is what we wanted
    assertEquals(oid(35).toString(), resp1.getString("id"));
    assertEquals("HOST_RESTARTED", resp1.getString("eventTypeName"));
    assertTrue(resp1.has("created"));
    // test if non-admin would get 401 error as the event has hidden field equaled true
    final JSONObject resp2 =
        doDigestJsonGet(
            "api/public/v1.0/orgs/" + oid(200) + "/events/" + oid(35),
            HttpStatus.SC_UNAUTHORIZED,
            JANE_DOE_USERNAME,
            JANE_DOE_API_KEY);
    assertEquals("USER_UNAUTHORIZED", resp2.getString("errorCode"));
    // test event not found exception when event doesn't exist
    final JSONObject resp3 =
        doDigestJsonGet(
            "api/public/v1.0/orgs/" + oid(200) + "/events/" + oid(404),
            HttpStatus.SC_NOT_FOUND,
            JANE_DOE_USERNAME,
            JANE_DOE_API_KEY);
    assertEquals("ORG_EVENT_NOT_FOUND", resp3.getString("errorCode"));
    // test event not found exception when event exist but orgId doesn't match
    final JSONObject resp4 =
        doDigestJsonGet(
            "api/public/v1.0/orgs/" + oid(204) + "/events/" + oid(36),
            HttpStatus.SC_NOT_FOUND,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    assertEquals("ORG_EVENT_NOT_FOUND", resp4.getString("errorCode"));
    // test if A non-global user can access a non-hidden event
    final JSONObject resp5 =
        doDigestJsonGet(
            "api/public/v1.0/orgs/" + oid(200) + "/events/" + oid(34),
            HttpStatus.SC_OK,
            JANE_DOE_USERNAME,
            JANE_DOE_API_KEY);
    assertEquals(oid(34).toString(), resp5.getString("id"));
    assertEquals("MONITORING_AGENT_DOWN", resp5.getString("eventTypeName"));
    assertTrue(resp5.has("created"));
    // test if the event has groupId but it is not an org event and we will get 401 error
    final JSONObject resp6 =
        doDigestJsonGet(
            "api/public/v1.0/orgs/" + oid(200) + "/events/" + oid(16),
            HttpStatus.SC_NOT_FOUND,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    assertEquals("ORG_EVENT_NOT_FOUND", resp6.getString("errorCode"));
    // test if the event has groupID but it's an org event
    final JSONObject resp7 =
        doDigestJsonGet(
            "api/public/v1.0/orgs/" + oid(200) + "/events/" + oid(24),
            HttpStatus.SC_OK,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    assertEquals(oid(24).toString(), resp7.getString("id"));
    assertEquals("ORG_CREDIT_CARD_ADDED", resp7.getString("eventTypeName"));
    assertEquals(oid(100).toString(), resp7.getString("groupId"));
    assertTrue(resp7.has("created"));
  }

  @Test
  public void testGetOrgEvent_withIncludeRaw() {
    // test if admin can access the endpoint and hidden event
    final JSONObject resp =
        doDigestJsonGet(
            "api/public/v1.0/orgs/" + oid(200) + "/events/" + oid(35) + "?includeRaw=true",
            HttpStatus.SC_OK,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    // test whether the event is what we wanted
    assertEquals(oid(35).toString(), resp.getString("id"));
    assertEquals("HOST_RESTARTED", resp.getString("eventTypeName"));
    assertTrue(resp.has("created"));
    assertTrue(resp.has(RAW_EVENT_FIELD));
  }

  @Test
  public void testGetAllOrgEvents() {
    // test if admin can access all events
    final JSONObject resp1 =
        doDigestJsonGet(
            "api/public/v1.0/orgs/" + oid(200) + "/events",
            HttpStatus.SC_OK,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    final JSONArray resultArray1 = resp1.getJSONArray("results");
    assertEquals(4, resultArray1.length());
    hasEvents(resultArray1, 24, 36, 34, 35);
    // test if hidden event will be omitted for non-admin
    final JSONObject resp2 =
        doDigestJsonGet(
            "api/public/v1.0/orgs/" + oid(200) + "/events",
            HttpStatus.SC_OK,
            JANE_DOE_USERNAME,
            JANE_DOE_API_KEY);
    final JSONArray resultArray2 = resp2.getJSONArray("results");
    assertEquals(3, resultArray2.length());
    hasEvents(resultArray2, 24, 36, 34);
    // test if non-member will get 401 error
    final JSONObject resp3 =
        doDigestJsonGet(
            "api/public/v1.0/orgs/" + oid(200) + "/events",
            HttpStatus.SC_UNAUTHORIZED,
            SIDNEY_DOE_USERNAME,
            SIDNEY_DOE_API_KEY);
    assertEquals("USER_CANNOT_ACCESS_ORG", resp3.getString("errorCode"));
    // test eventType query parameters
    final JSONObject resp4 =
        doDigestJsonGet(
            "api/public/v1.0/orgs/"
                + oid(200)
                + "/events?eventType=ORG_CREDIT_CARD_ADDED&eventType=HOST_RESTARTED",
            HttpStatus.SC_OK,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    final JSONArray resultArray4 = resp4.getJSONArray("results");
    assertEquals(2, resultArray4.length());
    hasEvents(resultArray4, 24, 35);
    // test minDate query parameter
    final JSONObject resp5 =
        doDigestJsonGet(
            "api/public/v1.0/orgs/"
                + oid(200)
                + "/events?minDate="
                + URLEncoder.encode(getDateNowAtMidnight(-10), StandardCharsets.UTF_8),
            HttpStatus.SC_OK,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    final JSONArray resultArray5 = resp5.getJSONArray("results");
    assertEquals(3, resultArray5.length());
    hasEvents(resultArray5, 24, 36, 34);
    // test maxDate query parameter
    final JSONObject resp6 =
        doDigestJsonGet(
            "api/public/v1.0/orgs/"
                + oid(200)
                + "/events?maxDate="
                + URLEncoder.encode(getDateNowAtMidnight(-3), StandardCharsets.UTF_8),
            HttpStatus.SC_OK,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    final JSONArray resultArray6 = resp6.getJSONArray("results");
    assertEquals(2, resultArray6.length());
    hasEvents(resultArray6, 34, 35);
    // test minDate and maxDate parameters
    final JSONObject resp7 =
        doDigestJsonGet(
            "api/public/v1.0/orgs/"
                + oid(200)
                + "/events?maxDate="
                + URLEncoder.encode(getDateNowAtMidnight(-3), StandardCharsets.UTF_8)
                + "&minDate="
                + URLEncoder.encode(getDateNowAtMidnight(-10), StandardCharsets.UTF_8),
            HttpStatus.SC_OK,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    final JSONArray resultArray7 = resp7.getJSONArray("results");
    assertEquals(1, resultArray7.length());
    hasEvents(resultArray7, 34);
    // test all minDate, maxDate and eventType parameters all together
    final JSONObject resp8 =
        doDigestJsonGet(
            "api/public/v1.0/orgs/"
                + oid(200)
                + "/events?maxDate="
                + URLEncoder.encode(getDateNowAtMidnight(-3), StandardCharsets.UTF_8)
                + "&minDate="
                + URLEncoder.encode(getDateNowAtMidnight(-10), StandardCharsets.UTF_8)
                + "&eventType=MONITORING_AGENT_DOWN",
            HttpStatus.SC_OK,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    final JSONArray resultArray8 = resp8.getJSONArray("results");
    assertEquals(1, resultArray8.length());
    hasEvents(resultArray8, 34);
    // test INVALID_DATE_FORMAT exception
    final JSONObject resp9 =
        doDigestJsonGet(
            "api/public/v1.0/orgs/" + oid(200) + "/events?minDate=10:22:31-06/07/2018",
            HttpStatus.SC_BAD_REQUEST,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    assertEquals("INVALID_DATE_FORMAT", resp9.getString("errorCode"));
  }

  @Test
  public void testGetOrgEvents_withIncludeRaw() {
    // test if admin can access all events
    final JSONObject resp1 =
        doDigestJsonGet(
            "api/public/v1.0/orgs/" + oid(200) + "/events?includeRaw=true",
            HttpStatus.SC_OK,
            ADMIN_USERNAME,
            ADMIN_API_KEY);
    final JSONArray resultArray1 = resp1.getJSONArray("results");
    assertEquals(4, resultArray1.length());
    hasEvents(resultArray1, 24, 36, 34, 35);
    for (int i = 0; i < resultArray1.length(); i++) {
      assertTrue(resultArray1.getJSONObject(0).has(RAW_EVENT_FIELD));
    }
  }

  private void hasEvents(final JSONArray pArray, final Integer... pEventIds) throws JSONException {
    final List<String> ids =
        StreamSupport.stream(
                Spliterators.spliteratorUnknownSize(pArray.iterator(), Spliterator.ORDERED), false)
            .map(jsonObject -> ((JSONObject) jsonObject).getString("id"))
            .collect(Collectors.toList());
    assertThat(
        ids, containsInAnyOrder(Stream.of(pEventIds).map((id) -> oid(id).toString()).toArray()));
  }

  private String getDateNowAtMidnight(int pDays) {
    final Date today = new Date();
    final Date date = DateUtils.addDays(today, pDays);
    return TimeUtils2.toISOString(date);
  }
}
