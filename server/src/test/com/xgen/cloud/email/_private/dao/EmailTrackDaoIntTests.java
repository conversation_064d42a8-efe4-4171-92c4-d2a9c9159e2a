package com.xgen.cloud.email._private.dao;

import com.xgen.cloud.common.model._public.email.EmailTrack;
import com.xgen.svc.common.TestDataUtils;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import jakarta.inject.Inject;
import java.util.Date;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class EmailTrackDaoIntTests extends JUnit5BaseSvcTest {

  @Inject private EmailTrackDao _emailTrackDao;

  @BeforeEach
  @Override
  public void setUp() throws Exception {
    super.setUp();
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "core/dao/email/EmailTrackDao/emailTrack.json.ftl",
        null,
        EmailTrack.DB_NAME,
        EmailTrack.COLLECTION_NAME);
  }

  @Test
  public void testSave() {
    final EmailTrack.Builder builder = new EmailTrack.Builder();
    builder.emailId(oid(12));
    builder.type(EmailTrack.Type.LINK);
    builder.groupId(oid(100));
    builder.recipient("<EMAIL>");
    builder.template("SOME_TEMPLATE");
    builder.linkName("SOME_LINK");
    builder.url("/someResource/someMethod");
    final Date now = new Date();
    builder.trackDate(now);
    getEmailTrackDao().save(builder.build());

    final List<EmailTrack> emailTracks = getEmailTrackDao().findByEmailId(oid(12));
    Assertions.assertEquals(1, emailTracks.size());
    final EmailTrack emailTrack = emailTracks.get(0);
    Assertions.assertNotNull(emailTrack.getId());
    Assertions.assertEquals(oid(12), emailTrack.getEmailId());
    Assertions.assertEquals(EmailTrack.Type.LINK, emailTrack.getType());
    Assertions.assertEquals(oid(100), emailTrack.getGroupId());
    Assertions.assertEquals("<EMAIL>", emailTrack.getRecipient());
    Assertions.assertEquals("SOME_TEMPLATE", emailTrack.getTemplate());
    Assertions.assertEquals("SOME_LINK", emailTrack.getLinkName());
    Assertions.assertEquals("/someResource/someMethod", emailTrack.getUrl());
    Assertions.assertEquals(now, emailTrack.getTrackDate());
  }

  @Test
  public void testFindById() {
    final EmailTrack emailTrack = getEmailTrackDao().findById(oid(1));
    Assertions.assertEquals(oid(1), emailTrack.getId());
    Assertions.assertEquals(oid(10), emailTrack.getEmailId());
    Assertions.assertEquals(EmailTrack.Type.LINK, emailTrack.getType());
    Assertions.assertEquals(oid(100), emailTrack.getGroupId());
    Assertions.assertEquals("<EMAIL>", emailTrack.getRecipient());
    Assertions.assertEquals("SOME_TEMPLATE", emailTrack.getTemplate());
    Assertions.assertEquals("SOME_LINK", emailTrack.getLinkName());
    Assertions.assertEquals("/someResource/someMethod", emailTrack.getUrl());
    Assertions.assertNotNull(emailTrack.getTrackDate());
  }

  @Test
  public void testFindByEmailId() {
    final List<EmailTrack> emailTracks = getEmailTrackDao().findByEmailId(oid(10));
    Assertions.assertEquals(2, emailTracks.size());
    Assertions.assertEquals(oids(1, 2), extractIds(emailTracks));
  }

  protected EmailTrackDao getEmailTrackDao() {
    return _emailTrackDao;
  }
}
