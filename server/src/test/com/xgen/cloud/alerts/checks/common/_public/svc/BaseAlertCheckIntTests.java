package com.xgen.cloud.alerts.checks.common._public.svc;

import com.xgen.cloud.alerts.checks.group._public.svc.GroupAlertCheckRegistry;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.mms.svc.alert.check.agent.MonitoringAgentVersionBehind;
import com.xgen.svc.mms.svc.alert.check.host.HostHasIndexSuggestions;
import jakarta.inject.Inject;
import java.util.Set;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

public class BaseAlertCheckIntTests extends JUnit5BaseSvcTest {
  @Inject private GroupAlertCheckRegistry _groupAlertCheckRegistry;

  @Test
  public void testGetAlertSchedulePropertyClassName() {
    Set<AlertCheck> alertCheckSet = _groupAlertCheckRegistry.getAllAlertChecks();
    for (AlertCheck alertCheck : alertCheckSet) {
      if (alertCheck instanceof HostHasIndexSuggestions) {
        Assertions.assertEquals(
            BaseAlertCheck.getAlertSchedulePropertyClassName(alertCheck.getClass()),
            "mms.alerts.HostHasIndexSuggestions.frequency");
      } else if (alertCheck instanceof MonitoringAgentVersionBehind) {
        Assertions.assertEquals(
            BaseAlertCheck.getAlertSchedulePropertyClassName(alertCheck.getClass()),
            "mms.alerts.MonitoringAgentVersionBehind.frequency");
      }
    }
  }
}
