package com.xgen.cloud.brs.core._public.util;

import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import com.mongodb.WriteConcern;
import com.xgen.cloud.brs.GridTestConstants;
import com.xgen.cloud.brs.core._public.model.SnapshotStoreIdentifier;
import com.xgen.cloud.brs.core._public.model.daemon.ReplicaState;
import com.xgen.cloud.brs.daemon._public.grid.BackupDaemonConfiguration;
import com.xgen.cloud.brs.daemon._public.grid.BackupInjector;
import com.xgen.cloud.brs.daemon._public.grid.dao.ImplicitJobDaoV2;
import com.xgen.cloud.common.brs._public.model.SnapshotStoreType;
import com.xgen.cloud.common.util._public.time.TimeUtils;
import com.xgen.cloud.monitoring.common._public.model.StorageEngine;
import com.xgen.svc.brs.grid.GridTestHelpers;
import org.bson.types.BSONTimestamp;
import org.bson.types.ObjectId;

public class SnapshotJobTestHelpers {
  static final BackupDaemonConfiguration CONFIG = GridTestHelpers.getConfig();
  private static final BackupInjector BACKUP_INJECTOR = GridTestHelpers.getBackupInjector();
  static final ObjectId GROUP_ID = new ObjectId("4f84ca47e4b0c083000cc4ae");
  static final String RS_ID = "rsId";

  public static void putNewSnapshotJob(
      StorageEngine storageEngine, SnapshotStoreType snapshotStoreType, String blockstoreId) {
    ImplicitJobDaoV2 jobDao = BACKUP_INJECTOR.getImplicitJobDaoV2();

    GridTestHelpers.createReplicaSet(
        BACKUP_INJECTOR,
        GROUP_ID,
        RS_ID,
        SnapshotStoreIdentifier.of(snapshotStoreType, blockstoreId),
        storageEngine,
        GridTestConstants.DEFAULT_VERSION_STRING,
        GridTestHelpers.getMongoTestOptions());
    jobDao.bindJobToMachine(GROUP_ID, RS_ID, CONFIG.getMachine(), null);

    DBObject snapshotJobDoc = jobDao.getJobDocument(GROUP_ID, RS_ID);
    int now = (int) (System.currentTimeMillis() / 1000L);
    BSONTimestamp snapshotTimestamp = new BSONTimestamp(now + TimeUtils.SECONDS_PER_HOUR * 6, 1);

    DBObject stateDoc = new BasicDBObject("action", ReplicaState.SNAPSHOT.getAction());
    stateDoc.put("scheduledSnapshot", snapshotTimestamp);
    snapshotJobDoc.put("state", stateDoc);
    snapshotJobDoc.put("nextSnapshot", snapshotTimestamp);
    snapshotJobDoc.put("head", new BSONTimestamp(now - 1, 1));
    snapshotJobDoc.put("head_h", 1000);

    DBObject blockstore = new BasicDBObject("id", blockstoreId);
    blockstore.put("phase", "A");
    blockstore.put("lastGroomedMS", 0L);
    snapshotJobDoc.put(blockstoreId, blockstore);

    DBObject machine = (DBObject) snapshotJobDoc.get("machine");
    machine.put("owned", CONFIG.getMachine().toDBObject());

    jobDao.getDbCollection().save(snapshotJobDoc, WriteConcern.ACKNOWLEDGED);
    GridTestHelpers.initStandardSnapshotSchedule(
        BACKUP_INJECTOR, now, jobDao.getBackupStatus(GROUP_ID, RS_ID));
  }
}
