package com.xgen.cloud.migration._private.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.mongodb.BasicDBObject;
import com.xgen.cloud.migration._public.svc.MigrationSvc;
import com.xgen.svc.common.TestDataUtils;
import jakarta.inject.Inject;
import java.util.Collection;
import java.util.List;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class RenameClusterDescriptionFlexMigrationStateFieldMigrationIdempotentMigrationIntTests
    extends BaseIdempotentMigrationTest<RenameClusterDescriptionFlexMigrationStateFieldMigration> {
  @Inject private RenameClusterDescriptionFlexMigrationStateFieldMigration _migration;
  @Inject private MigrationSvc _migrationSvc;

  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/migration/RenameClusterDescriptionFlexMigrationStateFieldMigration/clusterDescriptions.json.ftl",
        null,
        RenameClusterDescriptionFlexMigrationStateFieldMigration.DB_NAME,
        RenameClusterDescriptionFlexMigrationStateFieldMigration.COLLECTION_NAME);
  }

  @Override
  protected RenameClusterDescriptionFlexMigrationStateFieldMigration getMigration() {
    return _migration;
  }

  @Override
  protected Collection<?> findAllRecords() {
    return getMigration().getDao().findAllAsBasicList();
  }

  @Test
  public void testMigration() {
    final List<ObjectId> docsBeforeMigrationNoChange =
        findAllRecords().stream()
            .map(doc -> (BasicDBObject) doc)
            .filter(
                doc ->
                    !doc.containsField(
                            RenameClusterDescriptionFlexMigrationStateFieldMigration.OLD_FIELD)
                        && !doc.containsField(
                            RenameClusterDescriptionFlexMigrationStateFieldMigration.NEW_FIELD))
            .map(doc -> doc.getObjectId("uniqueId"))
            .toList();

    final List<ObjectId> docsBeforeMigrationNeedChange =
        findAllRecords().stream()
            .map(doc -> (BasicDBObject) doc)
            .filter(
                doc ->
                    doc.containsField(
                        RenameClusterDescriptionFlexMigrationStateFieldMigration.OLD_FIELD))
            .map(doc -> doc.getObjectId("uniqueId"))
            .toList();
    assertEquals(
        docsBeforeMigrationNeedChange.size(), getMigration().findDocumentsToMigrate().count());
    _migrationSvc.runThese(List.of(getMigration()));

    assertEquals(docsBeforeMigrationNeedChange.size(), getMigrationResult().getNumUpdated());
    for (BasicDBObject doc : findAllRecords().stream().map(doc -> (BasicDBObject) doc).toList()) {
      if (docsBeforeMigrationNoChange.contains(doc.getObjectId("uniqueId"))) {
        assertFalse(
            doc.containsField(RenameClusterDescriptionFlexMigrationStateFieldMigration.NEW_FIELD));
        assertFalse(
            doc.containsField(RenameClusterDescriptionFlexMigrationStateFieldMigration.OLD_FIELD));
      } else {
        assertTrue(
            doc.containsField(RenameClusterDescriptionFlexMigrationStateFieldMigration.NEW_FIELD));
        assertFalse(
            doc.containsField(RenameClusterDescriptionFlexMigrationStateFieldMigration.OLD_FIELD));
      }
    }
  }
}
