load("//systems/common/java:rules.bzl", "java_common_library")

java_common_library(
    name = "guice",
    srcs = glob(["*.java"]),
    deps = [
        "//systems/common/java/src/main/java/com/xgen/common/http",
        "//systems/common/java/src/main/java/com/xgen/common/main/processes",
        "@maven_common//:com_google_inject_guice",
        "@maven_common//:jakarta_servlet_jakarta_servlet_api",
        "@maven_common//:org_apache_commons_commons_configuration2",
        "@maven_common//:org_eclipse_jetty_jetty_server",
        "@maven_common//:org_eclipse_jetty_jetty_util",
        "@maven_common//:org_slf4j_slf4j_api",
    ],
)
