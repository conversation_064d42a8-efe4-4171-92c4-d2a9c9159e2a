from dataclasses import dataclass, field
from typing import Any

from pct.release_state.release_state import BaseReleaseState


# FUTURE: Refactor release state types
@dataclass
class AtlasCLIReleaseState(BaseReleaseState):
    # pylint: disable=duplicate-code

    published: bool = False
    qa_pass: bool = False
    allowed_to_publish: bool = False
    release_commit_sha: str | None = None  # Points at the release commit-sha in master
    metadata: dict[str, dict[str, Any]] = field(
        default_factory=dict
    )  # Stages are free to add metadata to this section as they need

    def update_metadata(self, name: str, **args: Any) -> None:
        """updates the metadata release state field"""
        if name not in self.metadata:
            self.metadata[name] = {}

        self.metadata[name].update(args)

    @staticmethod
    def get_release_name() -> str:
        """Return the release name"""
        return "Atlas CLI"
